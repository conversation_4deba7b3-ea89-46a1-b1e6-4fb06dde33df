/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file           : main.c
  * @brief          : Main program body
  ******************************************************************************
  * @attention
  *
  * <h2><center>&copy; Copyright (c) 2024 STMicroelectronics.
  * All rights reserved.</center></h2>
  *
  * This software component is licensed by ST under BSD 3-Clause license,
  * the "License"; You may not use this file except in compliance with the
  * License. You may obtain a copy of the License at:
  *                        opensource.org/licenses/BSD-3-Clause
  *
  ******************************************************************************
  */
/* USER CODE END Header */
/* Includes ------------------------------------------------------------------*/
#include "main.h"
#include "dma.h"
#include "tim.h"
#include "usart.h"
#include "gpio.h"

/* Private includes ----------------------------------------------------------*/
/* USER CODE BEGIN Includes */
#include "string.h"  //字符串处理函数库
#include <stdio.h>   //标准输入输出库
#include <stdlib.h>  //标准库函数
#define RE_SIZE 43   //UART1接收数据缓冲区大小
#define LENGTH 200   //ESP8266通信缓冲区大小
/* USER CODE END Includes */

/* Private typedef -----------------------------------------------------------*/
/* USER CODE BEGIN PTD */
__IO uint32_t Tick = 0;                    //系统时钟计数器
uint16_t Num = 0;                          //数据计数器
uint16_t Num_Now = 0;                      //当前数据计数
uint8_t Uart_Flag = 0;                     //UART1数据接收完成标志
uint8_t Re_Data[RE_SIZE] = {0};            //UART1接收数据缓冲区
uint8_t Tx_Data[40] = {0};                 //UART1发送数据缓冲区

/* USER CODE END PTD */

/* Private define ------------------------------------------------------------*/
/* USER CODE BEGIN PD */
/* USER CODE END PD */

/* Private macro -------------------------------------------------------------*/
/* USER CODE BEGIN PM */

/* USER CODE END PM */

/* Private variables ---------------------------------------------------------*/

/* USER CODE BEGIN PV */

uint8_t RxBuffer[LENGTH];                  //ESP8266通信接收缓冲区
uint8_t RecCount = 0;                      //接收数据计数器
uint8_t RxFlag = 0;                        //接收完成标志

/* USER CODE END PV */

/* Private function prototypes -----------------------------------------------*/
void SystemClock_Config(void);
/* USER CODE BEGIN PFP */

/* USER CODE END PFP */

/* Private user code ---------------------------------------------------------*/
/* USER CODE BEGIN 0 */

/* USER CODE END 0 */

/**
  * @brief  The application entry point.
  * @retval int
  */
int main(void)
{
  /* USER CODE BEGIN 1 */

  /* USER CODE END 1 */

  /* MCU Configuration--------------------------------------------------------*/

  /* Reset of all peripherals, Initializes the Flash interface and the Systick. */
  HAL_Init();

  /* USER CODE BEGIN Init */

  /* USER CODE END Init */

  /* Configure the system clock */
  SystemClock_Config();

  /* USER CODE BEGIN SysInit */
char T_data[LENGTH];                       //AT指令和MQTT消息构造缓冲区
  /* USER CODE END SysInit */

  /* Initialize all configured peripherals */
  MX_GPIO_Init();                          //GPIO端口初始化
  MX_DMA_Init();                           //DMA控制器初始化
  MX_TIM1_Init();                          //定时器1初始化
  MX_USART1_UART_Init();                   //UART1初始化(数据接收)
  MX_USART2_UART_Init();                   //UART2初始化(ESP8266通信)
  /* USER CODE BEGIN 2 */
//ESP8266初始化和WiFi连接配置序列
//  __HAL_UART_ENABLE_IT(&huart1, UART_IT_IDLE);
//  HAL_UART_Receive_DMA(&huart1, (uint8_t*)RxBuffer, LENGTH);
	HAL_UART_Transmit(&huart2,"AT+RST\r\n",strlen("AT+RST\r\n"),100);                                                                    //复位ESP8266模块
	HAL_Delay(2000);                                                                                                                     //等待复位完成
	HAL_UART_Transmit(&huart2,"AT+CWDHCP=1,1\r\n",strlen("AT+CWDHCP=1,1\"\r\n"),100);                                                   //启用DHCP客户端模式
	HAL_Delay(2000);                                                                                                                     //等待命令执行
	HAL_UART_Transmit(&huart2,"AT+CWMODE=1\r\n",strlen("AT+CWMODE=1\r\n"),100);                                                         //设置WiFi模式为Station模式
	HAL_Delay(2000);                                                                                                                     //等待模式设置完成
	HAL_UART_Transmit(&huart2,"AT+CWJAP=\"TP-LINK_43FF\",\"66668888\"\r\n",strlen("AT+CWJAP=\"TP-LINK_43FF\",\"66668888\"\r\n"),100);   //连接到指定WiFi网络
	HAL_Delay(5000);                                                                                                                     //等待WiFi连接建立
	HAL_UART_Transmit(&huart2,"AT+MQTTUSERCFG=0,1,\"ESP\",\"89Mif\",\"version=2018-10-31&res=proD\",0,0,\"\"\r\n",strlen("AT+MQTTUSERCFG=0,1,\"ESP\",\"89Mif\",\"version=2018-10-31&res=proD\",0,0,\"\"\"\r\n"),10000);
	HAL_Delay(2000);
	HAL_UART_Transmit(&huart2,"AT+MQTTCONN=0,\"broker.mqttdashboard.com\",1883,1\r\n",strlen("AT+MQTTCONN=0,\"broker.mqttdashboard.com\",1883,1\r\n"),1000);
	HAL_Delay(2000);
	sprintf(T_data,"AT+MQTTPUB=0,\"nitamade/jiushe/ge/shabi\",\"%s\",1,0\r\n","TT=657");
	HAL_UART_Transmit(&huart2,T_data,strlen(T_data),100);
	HAL_Delay(2000);
	HAL_UART_Transmit(&huart2,"AT+CIPMODE=1\r\n",strlen("AT+CIPMODE=1\r\n"),100);
	HAL_Delay(1000);
	HAL_UART_Receive_DMA(&huart1,Re_Data,RE_SIZE);
	HAL_UART_Receive_IT(&huart1,Tx_Data,1);
  /* USER CODE END 2 */
  /* Infinite loop */
  /* USER CODE BEGIN WHILE */
  while (1)
  {
	
		if(Uart_Flag)
		{
			sprintf(T_data,"AT+MQTTPUB=0,\"nitamade/jiushe/ge/shabi\",\"%s\",1,0\r\n",Re_Data);
			HAL_UART_Transmit(&huart2,T_data,strlen(T_data),1000);
//			HAL_UART_Transmit(&huart2,Re_Data,RE_SIZE,10);
//			memset(Re_Data,NULL,RE_SIZE);
			Uart_Flag = 0;
			HAL_UART_Receive_IT(&huart1,Tx_Data,1);
		}
		HAL_Delay(1);
    /* USER CODE END WHILE */

    /* USER CODE BEGIN 3 */

  }
  /* USER CODE END 3 */
}

/**
  * @brief System Clock Configuration
  * @retval None
  */
void SystemClock_Config(void)
{
  RCC_OscInitTypeDef RCC_OscInitStruct = {0};
  RCC_ClkInitTypeDef RCC_ClkInitStruct = {0};

  /** Initializes the RCC Oscillators according to the specified parameters
  * in the RCC_OscInitTypeDef structure.
  */
  RCC_OscInitStruct.OscillatorType = RCC_OSCILLATORTYPE_HSE;
  RCC_OscInitStruct.HSEState = RCC_HSE_ON;
  RCC_OscInitStruct.HSEPredivValue = RCC_HSE_PREDIV_DIV1;
  RCC_OscInitStruct.HSIState = RCC_HSI_ON;
  RCC_OscInitStruct.PLL.PLLState = RCC_PLL_ON;
  RCC_OscInitStruct.PLL.PLLSource = RCC_PLLSOURCE_HSE;
  RCC_OscInitStruct.PLL.PLLMUL = RCC_PLL_MUL9;
  if (HAL_RCC_OscConfig(&RCC_OscInitStruct) != HAL_OK)
  {
    Error_Handler();
  }
  /** Initializes the CPU, AHB and APB buses clocks
  */
  RCC_ClkInitStruct.ClockType = RCC_CLOCKTYPE_HCLK|RCC_CLOCKTYPE_SYSCLK
                              |RCC_CLOCKTYPE_PCLK1|RCC_CLOCKTYPE_PCLK2;
  RCC_ClkInitStruct.SYSCLKSource = RCC_SYSCLKSOURCE_PLLCLK;
  RCC_ClkInitStruct.AHBCLKDivider = RCC_SYSCLK_DIV1;
  RCC_ClkInitStruct.APB1CLKDivider = RCC_HCLK_DIV2;
  RCC_ClkInitStruct.APB2CLKDivider = RCC_HCLK_DIV1;

  if (HAL_RCC_ClockConfig(&RCC_ClkInitStruct, FLASH_LATENCY_2) != HAL_OK)
  {
    Error_Handler();
  }
}

/* USER CODE BEGIN 4 */
void HAL_UART_RxCpltCallback(UART_HandleTypeDef *huart)
{
	__HAL_UART_DISABLE_IT(huart,UART_IT_TC);
	HAL_UART_Receive_DMA(&huart1,Re_Data,RE_SIZE);
	Uart_Flag = 1;
}                             
/* USER CODE END 4 */        
/**
  * @brief  This function is executed in case of error occurrence.
  * @retval None
  */
void Error_Handler(void)
{
  /* USER CODE BEGIN Error_Handler_Debug */
  /* User can add his own implementation to report the HAL error return state */
  __disable_irq();
  while (1)
  {
  }
  /* USER CODE END Error_Handler_Debug */
}

#ifdef  USE_FULL_ASSERT
/**
  * @brief  Reports the name of the source file and the source line number
  *         where the assert_param error has occurred.
  * @param  file: pointer to the source file name
  * @param  line: assert_param error line source number
  * @retval None
  */
void assert_failed(uint8_t *file, uint32_t line)
{
  /* USER CODE BEGIN 6 */
  /* User can add his own implementation to report the file name and line number,
     ex: printf("Wrong parameters value: file %s on line %d\r\n", file, line) */
  /* USER CODE END 6 */
}
#endif /* USE_FULL_ASSERT */

/************************ (C) COPYRIGHT STMicroelectronics *****END OF FILE****/
