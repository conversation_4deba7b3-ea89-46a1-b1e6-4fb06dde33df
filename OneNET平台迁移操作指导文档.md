# OneNET平台迁移操作指导文档

## 📋 文档概述

本文档提供从HiveMQ公共服务器迁移到中国移动OneNET物联网平台的详细操作指导。包含平台配置、代码修改、功能增强、测试验证和项目部署的完整流程，确保初学者能够顺利完成平台迁移。

---

## 🌐 OneNET平台准备工作

### 第一步：OneNET账号注册和登录

1. **访问OneNET官网**
   - 网址：https://open.iot.10086.cn/
   - 点击右上角"注册"按钮

2. **完成账号注册**
   ```
   注册信息填写：
   ├── 手机号码：输入有效的中国大陆手机号
   ├── 验证码：点击获取短信验证码
   ├── 密码设置：至少8位，包含字母和数字
   └── 同意服务条款：勾选用户协议
   ```

3. **登录OneNET控制台**
   - 使用注册的手机号和密码登录
   - 首次登录需要完善个人信息

### 第二步：创建产品

1. **进入产品管理**
   ```
   导航路径：控制台 → 产品管理 → 创建产品
   ```

2. **填写产品信息**
   ```yaml
   产品配置参数：
   产品名称: "智能电表监控系统"
   产品描述: "基于Qt的智能电表数据采集和监控系统"
   所属分类: "智能家居" → "智能电表"
   节点类型: "设备"
   数据格式: "OneJSON"
   联网方式: "WiFi"
   接入协议: "MQTT"
   ```

3. **高级设置配置**
   ```yaml
   高级配置：
   数据存储: 开启（保留30天）
   设备鉴权: Token鉴权
   Topic权限: 默认权限
   数据推送: 关闭（可后续开启）
   ```

4. **获取产品信息**
   ```
   创建成功后记录以下信息：
   ├── 产品ID (Product ID): 例如 "123456"
   ├── 产品密钥 (Product Key): 用于设备注册
   └── 接入地址: mqtts.heclouds.com:1883
   ```

### 第三步：创建设备并获取三元组

1. **添加设备**
   ```
   导航路径：产品详情 → 设备管理 → 添加设备
   ```

2. **设备信息配置**
   ```yaml
   设备配置：
   设备名称: "SmartMeter_001"
   设备描述: "智能电表采集终端设备"
   鉴权信息: 自动生成Token
   设备位置: 可选填写
   ```

3. **获取设备三元组**
   ```yaml
   设备三元组信息（重要！请妥善保存）：
   产品ID (Product ID): "123456"
   设备名称 (Device Name): "SmartMeter_001"  
   设备Token (Token): "version=2018-10-31&res=products%2F123456%2Fdevices%2FSmartMeter_001&et=1234567890&method=md5&sign=abcdef1234567890"
   ```

### 第四步：OneNET平台界面功能介绍

1. **设备管理界面**
   ```
   功能模块：
   ├── 设备列表：查看所有设备状态
   ├── 设备详情：查看单个设备信息
   ├── 在线调试：测试设备通信
   ├── 数据流：查看实时数据
   └── 设备日志：查看连接和通信日志
   ```

2. **数据管理界面**
   ```
   数据功能：
   ├── 数据流展示：实时数据可视化
   ├── 历史数据：查询历史数据记录
   ├── 数据统计：数据分析和报表
   └── 数据导出：支持CSV、Excel格式
   ```

---

## 🔧 代码修改详细指导

### 修改前的准备工作

1. **备份原始代码**
   ```bash
   # 创建备份文件夹
   mkdir backup_$(date +%Y%m%d)
   # 复制整个项目
   cp -r QT_ShengFan backup_$(date +%Y%m%d)/
   ```

2. **创建配置文件**
   ```cpp
   // 在项目根目录创建 config.h 文件
   #ifndef CONFIG_H
   #define CONFIG_H

   // OneNET平台配置参数
   #define ONENET_SERVER "mqtts.heclouds.com"
   #define ONENET_PORT 1883
   #define PRODUCT_ID "123456"                    // 替换为你的产品ID
   #define DEVICE_NAME "SmartMeter_001"           // 替换为你的设备名称
   #define DEVICE_TOKEN "your_device_token_here"  // 替换为你的设备Token

   // MQTT主题配置
   #define PUBLISH_TOPIC "$sys/" PRODUCT_ID "/" DEVICE_NAME "/thing/property/post"
   #define SUBSCRIBE_TOPIC "$sys/" PRODUCT_ID "/" DEVICE_NAME "/thing/property/post/reply"

   #endif // CONFIG_H
   ```

### 第一步：修改服务器连接参数（mainwindow.cpp第31-32行）

**原始代码：**
```cpp
// 第31行
client->setHostname("broker.mqttdashboard.com");
// 第32行  
client->setPort(1883);
```

**修改后代码：**
```cpp
// 第31行 - 修改服务器地址
client->setHostname(ONENET_SERVER);
// 第32行 - 端口保持不变
client->setPort(ONENET_PORT);
```

### 第二步：添加OneNET认证配置（第32行后插入）

**在第32行后插入以下代码：**
```cpp
// OneNET平台认证配置（插入到第32行后）
client->setUsername(PRODUCT_ID);
client->setPassword(DEVICE_TOKEN);
client->setClientId(DEVICE_NAME);

// 设置连接选项
client->setKeepAlive(60);                    // 心跳间隔60秒
client->setCleanSession(true);               // 清除会话
client->setAutoKeepAlive(true);              // 自动心跳
```

### 第三步：修改MQTT主题格式（第82行）

**原始代码：**
```cpp
// 第82行
QMqttTopicFilter filter("nitamade/jiushe/ge/shabi");
```

**修改后代码：**
```cpp
// 第82行 - 修改为OneNET标准主题格式
QMqttTopicFilter filter(SUBSCRIBE_TOPIC);
```

### 第四步：重写数据解析函数（第274-281行）

**原始的Data_Take_Double函数：**
```cpp
double MainWindow::Data_Take_Double(QString Data, QString Data_name, QString Data_Last)
{
    double Value;
    Location_Fornt = Data.indexOf(Data_name, 0, Qt::CaseInsensitive);
    Location_Last = Data.indexOf(Data_Last, Location_Last+1, Qt::CaseInsensitive);
    Value = Data.mid((Location_Fornt + Data_name.length()), 
                     Location_Last - (Location_Fornt + Data_name.length())).toDouble();
    return Value;
}
```

**新增JSON解析函数（替换原函数）：**
```cpp
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonValue>

double MainWindow::Data_Take_Double_JSON(const QString &jsonData, const QString &paramName)
{
    // 解析JSON数据
    QJsonParseError parseError;
    QJsonDocument doc = QJsonDocument::fromJson(jsonData.toUtf8(), &parseError);
    
    if (parseError.error != QJsonParseError::NoError) {
        qDebug() << "JSON解析错误:" << parseError.errorString();
        return 0.0;
    }
    
    // 获取根对象
    QJsonObject rootObj = doc.object();
    
    // 检查是否包含params字段
    if (!rootObj.contains("params")) {
        qDebug() << "JSON中缺少params字段";
        return 0.0;
    }
    
    // 获取params对象
    QJsonObject paramsObj = rootObj["params"].toObject();
    
    // 提取指定参数的值
    if (paramsObj.contains(paramName)) {
        return paramsObj[paramName].toDouble();
    } else {
        qDebug() << "参数" << paramName << "不存在";
        return 0.0;
    }
}
```

### 第五步：修改数据接收处理函数（Data_Draw函数）

**原始的Data_Draw函数需要修改为：**
```cpp
void MainWindow::Data_Draw(const QMqttMessage &message)
{
    // 调试输出原始消息
    qDebug() << "收到MQTT消息:" << message.payload();

    // 将字节数组转换为QString
    QString jsonData = QString::fromUtf8(message.payload().data(), message.payload().size());

    // 使用新的JSON解析函数提取各个参数
    Home_1[add_data_num].Vol_Effective = Data_Take_Double_JSON(jsonData, "voltage");        // 电压
    Home_1[add_data_num].Electricity_Effective = Data_Take_Double_JSON(jsonData, "current"); // 电流
    Home_1[add_data_num].Power = Data_Take_Double_JSON(jsonData, "power");                   // 功率
    Home_1[add_data_num].Energy_Total = Data_Take_Double_JSON(jsonData, "energy");           // 总电能
    Home_1[add_data_num].Frequency = Data_Take_Double_JSON(jsonData, "frequency");           // 频率

    // 调试输出解析结果
    qDebug() << "解析结果 - 电压:" << Home_1[add_data_num].Vol_Effective;
    qDebug() << "解析结果 - 电流:" << Home_1[add_data_num].Electricity_Effective;
    qDebug() << "解析结果 - 功率:" << Home_1[add_data_num].Power;
    qDebug() << "解析结果 - 总电能:" << Home_1[add_data_num].Energy_Total;
    qDebug() << "解析结果 - 频率:" << Home_1[add_data_num].Frequency;

    // 更新数组索引（循环使用）
    add_data_num++;
    add_data_num %= 99999;

    // 输出完整JSON数据用于调试
    qDebug() << "完整JSON数据:" << jsonData;
}
```

### 第六步：在mainwindow.h中添加新函数声明

**在mainwindow.h的private slots:部分添加：**
```cpp
private slots:
    // 原有函数...
    void onConnected();
    void Data_Draw(const QMqttMessage &message);

    // 新增函数声明
    double Data_Take_Double_JSON(const QString &jsonData, const QString &paramName);
    void onMqttError(QMqttClient::ClientError error);  // 错误处理函数
    void onMqttStateChanged(QMqttClient::ClientState state); // 状态变化处理
```

### 第七步：添加错误处理和状态监控

**在MainWindow构造函数中添加更多信号槽连接：**
```cpp
// 在构造函数的信号槽连接部分添加
connect(client, &QMqttClient::errorChanged, this, &MainWindow::onMqttError);
connect(client, &QMqttClient::stateChanged, this, &MainWindow::onMqttStateChanged);
```

**添加错误处理函数实现：**
```cpp
void MainWindow::onMqttError(QMqttClient::ClientError error)
{
    QString errorMsg;
    switch(error) {
        case QMqttClient::NoError:
            errorMsg = "无错误";
            break;
        case QMqttClient::InvalidProtocolVersion:
            errorMsg = "协议版本无效";
            break;
        case QMqttClient::IdRejected:
            errorMsg = "客户端ID被拒绝";
            break;
        case QMqttClient::ServerUnavailable:
            errorMsg = "服务器不可用";
            break;
        case QMqttClient::BadUsernameOrPassword:
            errorMsg = "用户名或密码错误";
            break;
        case QMqttClient::NotAuthorized:
            errorMsg = "未授权访问";
            break;
        default:
            errorMsg = "未知错误";
            break;
    }

    qDebug() << "MQTT错误:" << errorMsg;
    sBar->showMessage("MQTT连接错误: " + errorMsg, 5000);
}

void MainWindow::onMqttStateChanged(QMqttClient::ClientState state)
{
    QString stateMsg;
    switch(state) {
        case QMqttClient::Disconnected:
            stateMsg = "已断开连接";
            break;
        case QMqttClient::Connecting:
            stateMsg = "正在连接...";
            break;
        case QMqttClient::Connected:
            stateMsg = "已连接到OneNET平台";
            break;
    }

    qDebug() << "MQTT状态:" << stateMsg;
    sBar->showMessage(stateMsg, 3000);
}
```

---

## 🔄 OneNET数据格式说明

### OneNET标准JSON格式

OneNET平台要求的数据格式为OneJSON，具体格式如下：

```json
{
    "id": "123456789",
    "version": "1.0",
    "params": {
        "voltage": 220.5,
        "current": 1.25,
        "power": 275.6,
        "energy": 1234.56,
        "frequency": 50.0
    }
}
```

### STM32端数据格式修改建议

**原始STM32发送格式：**
```c
"A123.45?B67.89?C234.56?D345.67?E50.12?"
```

**建议修改为OneJSON格式：**
```c
sprintf(json_buffer,
    "{"
    "\"id\":\"%ld\","
    "\"version\":\"1.0\","
    "\"params\":{"
        "\"voltage\":%.2f,"
        "\"current\":%.2f,"
        "\"power\":%.2f,"
        "\"energy\":%.2f,"
        "\"frequency\":%.2f"
    "}"
    "}",
    timestamp, voltage, current, power, energy, frequency);
```

---

## ⚙️ 功能增强建议

### 1. 配置文件管理类

**创建ConfigManager类（config.h）：**
```cpp
#ifndef CONFIGMANAGER_H
#define CONFIGMANAGER_H

#include <QString>
#include <QSettings>

class ConfigManager
{
public:
    static ConfigManager& getInstance();

    // OneNET配置
    QString getServer() const;
    int getPort() const;
    QString getProductId() const;
    QString getDeviceName() const;
    QString getDeviceToken() const;

    // 设置配置
    void setOneNetConfig(const QString &server, int port,
                        const QString &productId, const QString &deviceName,
                        const QString &token);

    // 保存和加载配置
    void saveConfig();
    void loadConfig();

private:
    ConfigManager();
    QSettings *settings;

    QString m_server;
    int m_port;
    QString m_productId;
    QString m_deviceName;
    QString m_deviceToken;
};

#endif // CONFIGMANAGER_H
```

### 2. 日志记录功能

**添加日志管理（logger.h）：**
```cpp
#ifndef LOGGER_H
#define LOGGER_H

#include <QObject>
#include <QFile>
#include <QTextStream>
#include <QDateTime>

class Logger : public QObject
{
    Q_OBJECT

public:
    enum LogLevel {
        Debug = 0,
        Info = 1,
        Warning = 2,
        Error = 3
    };

    static Logger& getInstance();
    void writeLog(LogLevel level, const QString &message);
    void setLogFile(const QString &fileName);

private:
    Logger(QObject *parent = nullptr);
    QFile *logFile;
    QTextStream *logStream;
};

// 使用宏简化日志调用
#define LOG_DEBUG(msg) Logger::getInstance().writeLog(Logger::Debug, msg)
#define LOG_INFO(msg) Logger::getInstance().writeLog(Logger::Info, msg)
#define LOG_WARNING(msg) Logger::getInstance().writeLog(Logger::Warning, msg)
#define LOG_ERROR(msg) Logger::getInstance().writeLog(Logger::Error, msg)

#endif // LOGGER_H
```

### 3. 自动重连机制

**添加重连功能：**
```cpp
// 在MainWindow类中添加成员变量
private:
    QTimer *reconnectTimer;
    int reconnectAttempts;
    static const int MAX_RECONNECT_ATTEMPTS = 5;

// 在构造函数中初始化
reconnectTimer = new QTimer(this);
reconnectTimer->setSingleShot(true);
connect(reconnectTimer, &QTimer::timeout, this, &MainWindow::attemptReconnect);

// 实现重连函数
void MainWindow::attemptReconnect()
{
    if (reconnectAttempts < MAX_RECONNECT_ATTEMPTS) {
        reconnectAttempts++;
        LOG_INFO(QString("尝试重连 OneNET 平台 (第%1次)").arg(reconnectAttempts));
        client->connectToHost();

        // 设置下次重连间隔（指数退避）
        int delay = qMin(30000, 1000 * qPow(2, reconnectAttempts));
        reconnectTimer->start(delay);
    } else {
        LOG_ERROR("达到最大重连次数，停止重连尝试");
        sBar->showMessage("连接失败，请检查网络和配置", 0);
    }
}
```

---

## 🧪 测试验证方法

### 第一步：连接测试

1. **编译和运行程序**
   ```bash
   # 在Qt Creator中编译项目
   # 或使用命令行编译
   qmake ShengFan.pro
   make
   ```

2. **检查连接状态**
   ```
   预期结果：
   ├── 状态栏显示："正在连接..."
   ├── 几秒后显示："已连接到OneNET平台"
   ├── 调试输出显示连接成功信息
   └── OneNET控制台显示设备在线
   ```

3. **连接失败排查**
   ```
   常见问题检查清单：
   ├── 网络连接是否正常
   ├── OneNET服务器地址是否正确
   ├── 产品ID和设备名称是否匹配
   ├── 设备Token是否有效
   └── 防火墙是否阻止1883端口
   ```

### 第二步：数据传输验证

1. **STM32端数据发送测试**
   ```json
   // 发送测试数据到OneNET
   {
       "id": "1234567890",
       "version": "1.0",
       "params": {
           "voltage": 220.5,
           "current": 1.25,
           "power": 275.6,
           "energy": 1234.56,
           "frequency": 50.0
       }
   }
   ```

2. **Qt端接收验证**
   ```
   检查项目：
   ├── 调试输出显示接收到JSON数据
   ├── 各参数解析正确（电压、电流、功率等）
   ├── 数据存储到Home_1数组
   ├── QCustomPlot图表更新显示
   └── 界面数值实时更新
   ```

3. **OneNET平台验证**
   ```
   平台检查：
   ├── 设备管理 → 设备详情 → 查看设备状态为"在线"
   ├── 数据管理 → 数据流 → 查看实时数据流
   ├── 设备日志 → 查看连接和数据传输日志
   └── 数据统计 → 验证数据接收频率和准确性
   ```

### 第三步：功能完整性测试

1. **界面交互测试**
   ```
   测试项目：
   ├── ComboBox切换不同参数显示
   ├── 图表缩放和拖拽功能
   ├── 状态栏信息显示
   └── 错误提示和处理
   ```

2. **异常情况测试**
   ```
   异常测试：
   ├── 网络断开重连测试
   ├── 无效JSON数据处理
   ├── 服务器连接超时处理
   └── 大量数据接收性能测试
   ```

### 第四步：性能和稳定性测试

1. **长时间运行测试**
   ```
   测试要求：
   ├── 连续运行24小时以上
   ├── 监控内存使用情况
   ├── 检查数据丢失情况
   └── 验证界面响应性能
   ```

2. **压力测试**
   ```
   压力测试：
   ├── 高频数据发送（每秒10次以上）
   ├── 大数据包传输测试
   ├── 多设备同时连接测试
   └── 网络不稳定环境测试
   ```

---

## 🚀 项目打包部署

### 第一步：依赖库整理

1. **收集必要的DLL文件**
   ```
   必需的Qt库文件：
   ├── Qt5Core.dll
   ├── Qt5Gui.dll
   ├── Qt5Widgets.dll
   ├── Qt5Network.dll
   ├── Qt5Mqtt.dll
   ├── Qt5PrintSupport.dll（QCustomPlot需要）
   └── platforms/qwindows.dll
   ```

2. **使用windeployqt工具**
   ```bash
   # 自动收集依赖库
   windeployqt.exe --debug --compiler-runtime ShengFan.exe
   ```

### 第二步：配置文件准备

1. **创建配置文件模板**
   ```ini
   # config.ini
   [OneNET]
   server=mqtts.heclouds.com
   port=1883
   product_id=请填写您的产品ID
   device_name=请填写您的设备名称
   device_token=请填写您的设备Token

   [Application]
   log_level=Info
   auto_reconnect=true
   max_reconnect_attempts=5
   ```

2. **创建使用说明文件**
   ```markdown
   # 使用说明.md

   ## 配置步骤
   1. 修改config.ini中的OneNET平台参数
   2. 确保STM32设备已连接到OneNET平台
   3. 运行ShengFan.exe程序
   4. 查看状态栏确认连接状态

   ## 故障排除
   - 连接失败：检查网络和OneNET配置
   - 数据异常：验证JSON格式和参数名称
   - 界面卡顿：检查数据接收频率
   ```

### 第三步：安装包制作

1. **使用NSIS制作安装包**
   ```nsis
   # installer.nsi
   !define APP_NAME "智能电表监控系统"
   !define APP_VERSION "2.0"
   !define APP_PUBLISHER "您的公司名称"

   OutFile "SmartMeter_Setup_v2.0.exe"
   InstallDir "$PROGRAMFILES\SmartMeter"

   Section "MainSection" SEC01
       SetOutPath "$INSTDIR"
       File "ShengFan.exe"
       File "config.ini"
       File "使用说明.md"
       File /r "platforms"
       File "*.dll"

       CreateDirectory "$SMPROGRAMS\智能电表监控系统"
       CreateShortCut "$SMPROGRAMS\智能电表监控系统\智能电表监控.lnk" "$INSTDIR\ShengFan.exe"
       CreateShortCut "$DESKTOP\智能电表监控.lnk" "$INSTDIR\ShengFan.exe"
   SectionEnd
   ```

2. **创建绿色版本**
   ```
   绿色版目录结构：
   SmartMeter_Portable/
   ├── ShengFan.exe
   ├── config.ini
   ├── 使用说明.md
   ├── platforms/
   │   └── qwindows.dll
   ├── Qt5Core.dll
   ├── Qt5Gui.dll
   ├── Qt5Widgets.dll
   ├── Qt5Network.dll
   ├── Qt5Mqtt.dll
   └── Qt5PrintSupport.dll
   ```

---

## 🔍 问题排查和调试指南

### 常见问题及解决方案

#### 1. 连接问题

**问题：无法连接到OneNET平台**
```
错误现象：
├── 状态栏显示"服务器不可用"
├── 调试输出显示连接超时
└── OneNET控制台设备显示离线

解决步骤：
1. 检查网络连接：ping mqtts.heclouds.com
2. 验证防火墙设置：确保1883端口未被阻止
3. 检查OneNET配置：
   - 产品ID是否正确
   - 设备名称是否匹配
   - Token是否有效且未过期
4. 查看OneNET设备日志确认错误原因
```

**问题：认证失败**
```
错误现象：
├── 状态栏显示"用户名或密码错误"
├── OneNET日志显示认证失败

解决方案：
1. 重新生成设备Token
2. 检查Token格式是否正确
3. 确认产品ID和设备名称匹配
4. 验证Token是否已过期
```

#### 2. 数据解析问题

**问题：JSON解析失败**
```
错误现象：
├── 调试输出显示"JSON解析错误"
├── 界面数据显示为0或异常值

解决步骤：
1. 检查STM32发送的JSON格式：
   - 确保JSON语法正确
   - 验证字段名称匹配
   - 检查数据类型是否正确
2. 使用在线JSON验证工具验证格式
3. 在OneNET平台查看原始数据
```

**问题：参数名称不匹配**
```
错误现象：
├── JSON解析成功但参数值为0
├── 调试输出显示"参数xxx不存在"

解决方案：
1. 对比STM32发送的参数名称和Qt解析的参数名称
2. 统一参数命名规范：
   - voltage（电压）
   - current（电流）
   - power（功率）
   - energy（总电能）
   - frequency（频率）
```

#### 3. 性能问题

**问题：界面卡顿或数据丢失**
```
原因分析：
├── 数据接收频率过高
├── JSON解析耗时过长
├── 绘图更新频率过高
└── 内存使用过多

优化方案：
1. 降低数据发送频率（建议1-5秒一次）
2. 优化JSON解析算法
3. 使用数据缓冲机制
4. 定期清理历史数据
```

### 调试技巧

#### 1. 启用详细日志

```cpp
// 在main函数中添加
QLoggingCategory::setFilterRules("qt.mqtt.* = true");

// 在关键位置添加调试输出
qDebug() << "MQTT连接状态:" << client->state();
qDebug() << "接收到的原始数据:" << message.payload();
qDebug() << "解析后的JSON:" << jsonData;
```

#### 2. 使用OneNET在线调试工具

```
调试步骤：
1. 登录OneNET控制台
2. 进入设备管理 → 设备详情
3. 点击"在线调试"
4. 发送测试数据验证通信
5. 查看设备日志分析问题
```

#### 3. 网络抓包分析

```bash
# 使用Wireshark抓包分析MQTT通信
# 过滤条件：tcp.port == 1883
# 关注CONNECT、CONNACK、PUBLISH、SUBSCRIBE等报文
```

---

## 📚 迁移对比总结

### 迁移前后对比

| 项目 | HiveMQ平台 | OneNET平台 |
|------|------------|------------|
| **服务器地址** | broker.mqttdashboard.com | mqtts.heclouds.com |
| **端口** | 1883 | 1883 |
| **认证方式** | 无认证 | Token认证 |
| **主题格式** | nitamade/jiushe/ge/shabi | $sys/{产品ID}/{设备名称}/thing/property/post |
| **数据格式** | 自定义字符串 | OneJSON格式 |
| **数据解析** | 字符串分割 | JSON解析 |
| **平台功能** | 基础MQTT转发 | 完整物联网平台 |
| **数据存储** | 无 | 30天历史数据 |
| **设备管理** | 无 | 完整设备生命周期管理 |
| **数据可视化** | 无 | 内置数据图表 |

### 迁移优势

✅ **企业级稳定性**：OneNET提供99.9%的服务可用性保证
✅ **数据安全性**：Token认证和加密传输保护数据安全
✅ **平台功能丰富**：设备管理、数据存储、可视化等一站式服务
✅ **技术支持完善**：中国移动提供专业技术支持
✅ **成本可控**：按量计费，小规模应用成本较低
✅ **扩展性强**：支持大规模设备接入和数据处理

### 注意事项

⚠️ **数据格式变更**：需要同步修改STM32端的数据发送格式
⚠️ **网络依赖**：需要稳定的互联网连接
⚠️ **学习成本**：需要熟悉OneNET平台的使用方法
⚠️ **调试复杂度**：相比简单MQTT服务器，调试步骤更多

---

## 🎯 迁移完成检查清单

### 代码修改检查

- [ ] 修改服务器地址为mqtts.heclouds.com
- [ ] 添加OneNET认证配置（用户名、密码、客户端ID）
- [ ] 修改MQTT主题为OneNET标准格式
- [ ] 实现JSON数据解析函数
- [ ] 更新数据接收处理逻辑
- [ ] 添加错误处理和状态监控
- [ ] 包含必要的头文件（QJsonDocument等）

### 平台配置检查

- [ ] OneNET账号注册并登录成功
- [ ] 创建产品并配置正确参数
- [ ] 添加设备并获取三元组信息
- [ ] 验证设备在OneNET控制台显示在线
- [ ] 测试数据传输和接收功能

### 功能测试检查

- [ ] MQTT连接建立成功
- [ ] 数据解析和显示正常
- [ ] 界面交互功能正常
- [ ] 错误处理机制有效
- [ ] 长时间运行稳定性良好

### 部署准备检查

- [ ] 收集所有依赖库文件
- [ ] 创建配置文件模板
- [ ] 编写用户使用说明
- [ ] 制作安装包或绿色版
- [ ] 测试在目标环境运行正常

---

## 📞 技术支持

如果在迁移过程中遇到问题，可以通过以下渠道获取帮助：

1. **OneNET官方文档**：https://open.iot.10086.cn/doc/
2. **OneNET技术论坛**：https://open.iot.10086.cn/bbs/
3. **Qt官方文档**：https://doc.qt.io/qt-5/qtmqtt-index.html
4. **MQTT协议规范**：http://docs.oasis-open.org/mqtt/mqtt/v3.1.1/

---

*本文档提供了从HiveMQ到OneNET平台的完整迁移指导，包含详细的操作步骤、代码示例和问题排查方法。按照本指南操作，您应该能够成功完成平台迁移并享受OneNET平台提供的丰富功能。*
