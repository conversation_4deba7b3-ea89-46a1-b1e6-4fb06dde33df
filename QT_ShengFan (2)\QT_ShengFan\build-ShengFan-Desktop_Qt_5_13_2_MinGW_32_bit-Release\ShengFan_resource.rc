#include <windows.h>

IDI_ICON1	ICON	DISCARDABLE	"E:\\Competition\\Photoelectric_Design\\QT_ShengFan\\ShengFan\\favicon.ico"

VS_VERSION_INFO VERSIONINFO
	FILEVERSION 0,0,0,0
	PRODUCTVERSION 0,0,0,0
	FILEFLAGSMASK 0x3fL
#ifdef _DEBUG
	FILEFLAGS VS_FF_DEBUG
#else
	FILEFLAGS 0x0L
#endif
	FILEOS VOS__WINDOWS32
	FILETYPE VFT_DLL
	FILESUBTYPE 0x0L
	BEGIN
		BLOCK "StringFileInfo"
		BEGIN
			BLOCK "040904b0"
			BEGIN
				VALUE "CompanyName", "\0"
				VALUE "FileDescription", "\0"
				VALUE "FileVersion", "0.0.0.0\0"
				VALUE "LegalCopyright", "\0"
				VALUE "OriginalFilename", "ShengFan.exe\0"
				VALUE "ProductName", "Sheng<PERSON><PERSON>\0"
				VALUE "ProductVersion", "0.0.0.0\0"
			END
		END
		BLOCK "VarFileInfo"
		BEGIN
			VALUE "Translation", 0x0409, 1200
		END
	END
/* End of Version info */

