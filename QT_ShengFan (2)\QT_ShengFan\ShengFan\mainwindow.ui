<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>800</width>
    <height>600</height>
   </rect>
  </property>
  <property name="focusPolicy">
   <enum>Qt::NoFocus</enum>
  </property>
  <property name="contextMenuPolicy">
   <enum>Qt::NoContextMenu</enum>
  </property>
  <property name="windowTitle">
   <string>盛帆杯</string>
  </property>
  <property name="windowIcon">
   <iconset resource="qrc.qrc">
    <normaloff>:/favicon.ico</normaloff>
    <normalon>:/favicon.ico</normalon>
    <activeon>:/favicon.ico</activeon>:/favicon.ico</iconset>
  </property>
  <widget class="QWidget" name="centralwidget">
   <layout class="QHBoxLayout" name="horizontalLayout">
    <item>
     <layout class="QVBoxLayout" name="verticalLayout">
      <item>
       <widget class="QFrame" name="frame">
        <property name="frameShape">
         <enum>QFrame::StyledPanel</enum>
        </property>
        <property name="frameShadow">
         <enum>QFrame::Raised</enum>
        </property>
        <layout class="QHBoxLayout" name="horizontalLayout_2" stretch="3">
         <item>
          <layout class="QVBoxLayout" name="verticalLayout_4">
           <item>
            <widget class="QFrame" name="frame_2">
             <property name="frameShape">
              <enum>QFrame::StyledPanel</enum>
             </property>
             <property name="frameShadow">
              <enum>QFrame::Raised</enum>
             </property>
             <layout class="QVBoxLayout" name="verticalLayout_7" stretch="1,20">
              <item>
               <layout class="QHBoxLayout" name="horizontalLayout_3">
                <item>
                 <widget class="QComboBox" name="Label">
                  <property name="iconSize">
                   <size>
                    <width>20</width>
                    <height>20</height>
                   </size>
                  </property>
                  <item>
                   <property name="text">
                    <string>电压有效值</string>
                   </property>
                  </item>
                  <item>
                   <property name="text">
                    <string>电流有效值</string>
                   </property>
                  </item>
                  <item>
                   <property name="text">
                    <string>频率</string>
                   </property>
                  </item>
                  <item>
                   <property name="text">
                    <string>功率</string>
                   </property>
                  </item>
                  <item>
                   <property name="text">
                    <string>能量</string>
                   </property>
                  </item>
                 </widget>
                </item>
                <item>
                 <spacer name="verticalSpacer">
                  <property name="orientation">
                   <enum>Qt::Vertical</enum>
                  </property>
                  <property name="sizeHint" stdset="0">
                   <size>
                    <width>20</width>
                    <height>40</height>
                   </size>
                  </property>
                 </spacer>
                </item>
                <item>
                 <widget class="QCheckBox" name="checkBox">
                  <property name="text">
                   <string>X轴自适应</string>
                  </property>
                  <property name="checked">
                   <bool>true</bool>
                  </property>
                 </widget>
                </item>
                <item>
                 <spacer name="verticalSpacer_2">
                  <property name="orientation">
                   <enum>Qt::Vertical</enum>
                  </property>
                  <property name="sizeHint" stdset="0">
                   <size>
                    <width>20</width>
                    <height>40</height>
                   </size>
                  </property>
                 </spacer>
                </item>
                <item>
                 <widget class="QCheckBox" name="checkBox_2">
                  <property name="text">
                   <string>Y轴自适应</string>
                  </property>
                 </widget>
                </item>
                <item>
                 <spacer name="horizontalSpacer_2">
                  <property name="orientation">
                   <enum>Qt::Horizontal</enum>
                  </property>
                  <property name="sizeHint" stdset="0">
                   <size>
                    <width>40</width>
                    <height>20</height>
                   </size>
                  </property>
                 </spacer>
                </item>
                <item>
                 <spacer name="horizontalSpacer">
                  <property name="orientation">
                   <enum>Qt::Horizontal</enum>
                  </property>
                  <property name="sizeHint" stdset="0">
                   <size>
                    <width>40</width>
                    <height>20</height>
                   </size>
                  </property>
                 </spacer>
                </item>
               </layout>
              </item>
              <item>
               <layout class="QVBoxLayout" name="verticalLayout_5">
                <item>
                 <widget class="QCustomPlot" name="plot1" native="true"/>
                </item>
               </layout>
              </item>
             </layout>
            </widget>
           </item>
          </layout>
         </item>
        </layout>
       </widget>
      </item>
     </layout>
    </item>
   </layout>
  </widget>
  <widget class="QMenuBar" name="menubar">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>0</y>
     <width>800</width>
     <height>21</height>
    </rect>
   </property>
  </widget>
  <widget class="QStatusBar" name="statusbar"/>
 </widget>
 <customwidgets>
  <customwidget>
   <class>QCustomPlot</class>
   <extends>QWidget</extends>
   <header location="global">qcustomplot.h</header>
   <container>1</container>
  </customwidget>
 </customwidgets>
 <resources>
  <include location="qrc.qrc"/>
 </resources>
 <connections/>
</ui>
