# MQTT服务器使用指南 - HiveMQ公共测试平台

## 1. 平台介绍

您学长使用的是 **HiveMQ公共MQTT测试服务器**，这是一个免费的在线MQTT代理服务器，专门用于开发和测试MQTT应用。

### 服务器信息
- **服务器地址**: `broker.mqttdashboard.com`
- **端口**: `1883` (非加密) / `8000` (WebSocket)
- **协议**: MQTT 3.1.1
- **访问方式**: 无需注册，完全免费

## 2. 如何访问云平台

### 方法一：通过网页客户端访问（推荐新手）

1. **打开浏览器**，访问：http://www.hivemq.com/demos/websocket-client/

2. **连接设置**：
   - Host: `broker.mqttdashboard.com`
   - Port: `8000`
   - Client ID: 可以自定义，比如 `WebClient_你的名字`
   - Username: 留空（可选）
   - Password: 留空（可选）

3. **点击 "Connect" 按钮**

4. **订阅主题**查看数据：
   - 在 "Subscriptions" 区域
   - Topic: `nitamade/jiushe/ge/shabi`
   - QoS: `0`
   - 点击 "Subscribe"

### 方法二：使用MQTT客户端软件

#### 推荐软件：MQTT Explorer
1. **下载安装**：
   - 访问：http://mqtt-explorer.com/
   - 下载并安装MQTT Explorer

2. **连接配置**：
   - Name: `学长的服务器`
   - Protocol: `mqtt://`
   - Host: `broker.mqttdashboard.com`
   - Port: `1883`
   - Username: 留空
   - Password: 留空

3. **连接并查看数据**：
   - 点击 "CONNECT"
   - 在左侧树形结构中找到 `nitamade/jiushe/ge/shabi`
   - 可以实时查看您的电表数据

## 3. 您的设备数据格式

根据学长的代码，您的电表数据会发送到：
- **主题**: `nitamade/jiushe/ge/shabi`
- **数据格式**: 直接的字符串数据（如：电压、电流等测量值）

## 4. 实时监控您的数据

### 使用网页客户端监控：
1. 按照方法一连接到服务器
2. 订阅主题 `nitamade/jiushe/ge/shabi`
3. 当您的STM32设备上电并连接WiFi后，就能在网页上看到实时数据

### 数据显示示例：
```
Topic: nitamade/jiushe/ge/shabi
Message: TT=657
Timestamp: 2024-01-15 14:30:25
```

## 5. 测试连接

### 手动发送测试消息：
1. 在MQTT客户端的 "Publish" 区域
2. Topic: `nitamade/jiushe/ge/shabi`
3. Message: `TEST=123`
4. QoS: `0`
5. 点击 "Publish"

如果连接正常，您应该能在订阅窗口看到这条测试消息。

## 6. 注意事项

⚠️ **重要提醒**：
- 这是**公共测试服务器**，任何人都可以访问
- **数据不会永久保存**，服务器重启后数据会丢失
- **不适合生产环境**，仅用于开发测试
- 其他人也可能看到您的数据

## 7. 常见问题

### Q: 为什么看不到数据？
A: 检查以下几点：
- STM32设备是否正常上电
- WiFi是否连接成功
- 确认订阅的主题名称正确

### Q: 数据显示乱码怎么办？
A: 检查字符编码设置，通常选择UTF-8

### Q: 连接失败怎么办？
A: 
- 检查网络连接
- 确认服务器地址和端口正确
- 尝试刷新页面重新连接

## 8. 下一步建议

如果您需要更稳定的服务，建议考虑：
1. 迁移到OneNET等专业物联网平台
2. 搭建自己的MQTT服务器
3. 使用阿里云、腾讯云等商业物联网平台

---

**联系方式**: 如有问题可以随时询问
**文档版本**: v1.0
**更新日期**: 2024年
