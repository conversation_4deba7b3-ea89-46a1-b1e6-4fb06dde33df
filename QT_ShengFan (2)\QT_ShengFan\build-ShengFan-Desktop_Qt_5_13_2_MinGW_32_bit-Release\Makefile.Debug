#############################################################################
# Makefile for building: ShengFan
# Generated by qmake (3.1) (Qt 5.13.2)
# Project:  ..\..\..\Photoelectric_Design\QT_ShengFan\ShengFan\ShengFan.pro
# Template: app
#############################################################################

MAKEFILE      = Makefile.Debug

EQ            = =

####### Compiler, tools and options

CC            = gcc
CXX           = g++
DEFINES       = -DUNICODE -D_UNICODE -DWIN32 -DMINGW_HAS_SECURE_API=1 -DQT_DEPRECATED_WARNINGS -DQT_PRINTSUPPORT_LIB -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_NETWORK_LIB -DQT_CORE_LIB -DQT_NEEDS_QMAIN
CFLAGS        = -fno-keep-inline-dllexport -g -Wall -W -Wextra $(DEFINES)
CXXFLAGS      = -fno-keep-inline-dllexport -g -std=gnu++11 -Wall -W -Wextra -fexceptions -mthreads $(DEFINES)
INCPATH       = -I..\..\..\Photoelectric_Design\QT_ShengFan\ShengFan -I. -I..\..\..\Photoelectric_Design\QT_ShengFan\ShengFan\include -I..\..\..\Photoelectric_Design\QT_ShengFan\ShengFan\include -I..\..\..\Photoelectric_Design\QT_ShengFan\ShengFan\include -I..\..\..\Photoelectric_Design\QT_ShengFan\ShengFan\include -I..\..\..\Photoelectric_Design\QT_ShengFan\ShengFan\mqtt -ID:\Software\QT5.13.2\5.13.2\mingw73_32\include -ID:\Software\QT5.13.2\5.13.2\mingw73_32\include\QtPrintSupport -ID:\Software\QT5.13.2\5.13.2\mingw73_32\include\QtWidgets -ID:\Software\QT5.13.2\5.13.2\mingw73_32\include\QtGui -ID:\Software\QT5.13.2\5.13.2\mingw73_32\include\QtANGLE -ID:\Software\QT5.13.2\5.13.2\mingw73_32\include\QtNetwork -ID:\Software\QT5.13.2\5.13.2\mingw73_32\include\QtCore -Idebug -I. -ID:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\win32-g++ 
LINKER      =        g++
LFLAGS        =        -Wl,-subsystem,windows -mthreads
LIBS        =        -LE:\Competition\Photoelectric_Design\QT_ShengFan\ShengFan\lib -lQt5Mqttd -lQt5Qmqttd D:\Software\QT5.13.2\5.13.2\mingw73_32\lib\libQt5PrintSupportd.a D:\Software\QT5.13.2\5.13.2\mingw73_32\lib\libQt5Widgetsd.a D:\Software\QT5.13.2\5.13.2\mingw73_32\lib\libQt5Guid.a D:\Software\QT5.13.2\5.13.2\mingw73_32\lib\libQt5Networkd.a D:\Software\QT5.13.2\5.13.2\mingw73_32\lib\libQt5Cored.a debug\ShengFan_resource_res.o  -lmingw32 D:\Software\QT5.13.2\5.13.2\mingw73_32\lib\libqtmaind.a -LC:\openssl\lib -LC:\Utils\my_sql\mysql-5.7.25-win32\lib -LC:\Utils\postgresql\pgsql\lib -lshell32 
QMAKE         = D:\Software\QT5.13.2\5.13.2\mingw73_32\bin\qmake.exe
IDC           = idc
IDL           = midl
ZIP           = zip -r -9
DEF_FILE      = 
RES_FILE      = debug\ShengFan_resource_res.o
COPY          = copy /y
SED           = $(QMAKE) -install sed
COPY_FILE     = copy /y
COPY_DIR      = xcopy /s /q /y /i
DEL_FILE      = del
DEL_DIR       = rmdir
MOVE          = move
CHK_DIR_EXISTS= if not exist
MKDIR         = mkdir
INSTALL_FILE    = copy /y
INSTALL_PROGRAM = copy /y
INSTALL_DIR     = xcopy /s /q /y /i
QINSTALL        = D:\Software\QT5.13.2\5.13.2\mingw73_32\bin\qmake.exe -install qinstall
QINSTALL_PROGRAM = D:\Software\QT5.13.2\5.13.2\mingw73_32\bin\qmake.exe -install qinstall -exe

####### Output directory

OBJECTS_DIR   = debug

####### Files

SOURCES       = ..\..\..\Photoelectric_Design\QT_ShengFan\ShengFan\main.cpp \
		..\..\..\Photoelectric_Design\QT_ShengFan\ShengFan\mainwindow.cpp \
		..\..\..\Photoelectric_Design\QT_ShengFan\ShengFan\qcustomplot.cpp debug\qrc_qrc.cpp \
		debug\moc_mainwindow.cpp \
		debug\moc_qcustomplot.cpp
OBJECTS       = debug/main.o \
		debug/mainwindow.o \
		debug/qcustomplot.o \
		debug/qrc_qrc.o \
		debug/moc_mainwindow.o \
		debug/moc_qcustomplot.o

DIST          =  ..\..\..\Photoelectric_Design\QT_ShengFan\ShengFan\mainwindow.h \
		..\..\..\Photoelectric_Design\QT_ShengFan\ShengFan\qcustomplot.h ..\..\..\Photoelectric_Design\QT_ShengFan\ShengFan\main.cpp \
		..\..\..\Photoelectric_Design\QT_ShengFan\ShengFan\mainwindow.cpp \
		..\..\..\Photoelectric_Design\QT_ShengFan\ShengFan\qcustomplot.cpp
QMAKE_TARGET  = ShengFan
DESTDIR        = debug\ #avoid trailing-slash linebreak
TARGET         = ShengFan.exe
DESTDIR_TARGET = debug\ShengFan.exe

####### Build rules

first: all
all: Makefile.Debug  debug/ShengFan.exe

debug/ShengFan.exe: E:/Competition/Photoelectric_Design/QT_ShengFan/ShengFan/lib/libQt5Qmqttd.a D:/Software/QT5.13.2/5.13.2/mingw73_32/lib/libQt5PrintSupportd.a D:/Software/QT5.13.2/5.13.2/mingw73_32/lib/libQt5Widgetsd.a D:/Software/QT5.13.2/5.13.2/mingw73_32/lib/libQt5Guid.a D:/Software/QT5.13.2/5.13.2/mingw73_32/lib/libQt5Networkd.a D:/Software/QT5.13.2/5.13.2/mingw73_32/lib/libQt5Cored.a D:/Software/QT5.13.2/5.13.2/mingw73_32/lib/libqtmaind.a ui_mainwindow.h $(OBJECTS) debug/ShengFan_resource_res.o
	$(LINKER) $(LFLAGS) -o $(DESTDIR_TARGET) $(OBJECTS)  $(LIBS)

debug/ShengFan_resource_res.o: ShengFan_resource.rc
	windres -i ShengFan_resource.rc -o debug\ShengFan_resource_res.o --include-dir=. $(DEFINES)

qmake: FORCE
	@$(QMAKE) -o Makefile.Debug ..\..\..\Photoelectric_Design\QT_ShengFan\ShengFan\ShengFan.pro -spec win32-g++ "CONFIG+=qtquickcompiler"

qmake_all: FORCE

dist:
	$(ZIP) ShengFan.zip $(SOURCES) $(DIST) ..\..\..\Photoelectric_Design\QT_ShengFan\ShengFan\ShengFan.pro D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\features\spec_pre.prf D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\qdevice.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\features\device_config.prf D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\common\sanitize.conf D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\common\gcc-base.conf D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\common\g++-base.conf D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\common\angle.conf D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\features\win32\windows_vulkan_sdk.prf D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\common\windows-vulkan.conf D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\common\g++-win32.conf D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\common\windows-desktop.conf D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\qconfig.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_3danimation.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_3danimation_private.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_3dcore.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_3dcore_private.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_3dextras.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_3dextras_private.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_3dinput.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_3dinput_private.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_3dlogic.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_3dlogic_private.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_3dquick.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_3dquick_private.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_3dquickanimation.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_3dquickanimation_private.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_3dquickextras.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_3dquickextras_private.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_3dquickinput.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_3dquickinput_private.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_3dquickrender.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_3dquickrender_private.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_3dquickscene2d.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_3dquickscene2d_private.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_3drender.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_3drender_private.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_accessibility_support_private.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_axbase.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_axbase_private.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_axcontainer.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_axcontainer_private.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_axserver.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_axserver_private.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_bluetooth.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_bluetooth_private.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_bodymovin_private.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_bootstrap_private.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_charts.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_charts_private.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_concurrent.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_concurrent_private.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_core.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_core_private.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_datavisualization.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_datavisualization_private.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_dbus.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_dbus_private.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_designer.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_designer_private.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_designercomponents_private.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_devicediscovery_support_private.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_edid_support_private.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_egl_support_private.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_eventdispatcher_support_private.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_fb_support_private.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_fontdatabase_support_private.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_gamepad.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_gamepad_private.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_gui.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_gui_private.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_help.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_help_private.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_location.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_location_private.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_multimedia.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_multimedia_private.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_multimediawidgets.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_multimediawidgets_private.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_network.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_network_private.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_networkauth.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_networkauth_private.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_nfc.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_nfc_private.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_opengl.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_opengl_private.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_openglextensions.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_openglextensions_private.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_packetprotocol_private.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_platformcompositor_support_private.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_positioning.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_positioning_private.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_positioningquick.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_positioningquick_private.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_printsupport.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_printsupport_private.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_purchasing.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_purchasing_private.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_qml.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_qml_private.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_qmldebug_private.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_qmldevtools_private.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_qmltest.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_qmltest_private.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_qtmultimediaquicktools_private.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_quick.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_quick_private.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_quickcontrols2.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_quickcontrols2_private.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_quickparticles_private.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_quickshapes_private.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_quicktemplates2.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_quicktemplates2_private.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_quickwidgets.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_quickwidgets_private.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_remoteobjects.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_remoteobjects_private.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_repparser.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_repparser_private.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_script.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_script_private.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_scripttools.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_scripttools_private.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_scxml.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_scxml_private.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_sensors.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_sensors_private.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_serialbus.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_serialbus_private.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_serialport.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_serialport_private.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_sql.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_sql_private.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_svg.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_svg_private.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_testlib.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_testlib_private.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_texttospeech.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_texttospeech_private.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_theme_support_private.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_uiplugin.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_uitools.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_uitools_private.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_virtualkeyboard.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_virtualkeyboard_private.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_webchannel.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_webchannel_private.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_websockets.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_websockets_private.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_widgets.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_widgets_private.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_windowsuiautomation_support_private.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_winextras.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_winextras_private.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_xml.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_xml_private.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_xmlpatterns.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\modules\qt_lib_xmlpatterns_private.pri D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\features\qt_functions.prf D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\features\qt_config.prf D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\win32-g++\qmake.conf D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\features\spec_post.prf .qmake.stash D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\features\exclusive_builds.prf D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\features\toolchain.prf D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\features\default_pre.prf D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\features\win32\default_pre.prf D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\features\resolve_config.prf D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\features\exclusive_builds_post.prf D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\features\default_post.prf D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\features\build_pass.prf D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\features\resources.prf D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\features\qtquickcompiler.prf D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\features\precompile_header.prf D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\features\warn_on.prf D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\features\qt.prf D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\features\moc.prf D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\features\win32\opengl.prf D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\features\uic.prf D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\features\qmake_use.prf D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\features\file_copies.prf D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\features\win32\windows.prf D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\features\testcase_targets.prf D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\features\exceptions.prf D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\features\yacc.prf D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\features\lex.prf ..\..\..\Photoelectric_Design\QT_ShengFan\ShengFan\ShengFan.pro ..\..\..\Photoelectric_Design\QT_ShengFan\ShengFan\qrc.qrc D:\Software\QT5.13.2\5.13.2\mingw73_32\lib\Qt5PrintSupportd.prl D:\Software\QT5.13.2\5.13.2\mingw73_32\lib\Qt5Widgetsd.prl D:\Software\QT5.13.2\5.13.2\mingw73_32\lib\Qt5Guid.prl D:\Software\QT5.13.2\5.13.2\mingw73_32\lib\Qt5Networkd.prl D:\Software\QT5.13.2\5.13.2\mingw73_32\lib\Qt5Cored.prl D:\Software\QT5.13.2\5.13.2\mingw73_32\lib\qtmaind.prl  ..\..\..\Photoelectric_Design\QT_ShengFan\ShengFan\qrc.qrc    D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\features\data\dummy.cpp ..\..\..\Photoelectric_Design\QT_ShengFan\ShengFan\mainwindow.h ..\..\..\Photoelectric_Design\QT_ShengFan\ShengFan\qcustomplot.h  ..\..\..\Photoelectric_Design\QT_ShengFan\ShengFan\main.cpp ..\..\..\Photoelectric_Design\QT_ShengFan\ShengFan\mainwindow.cpp ..\..\..\Photoelectric_Design\QT_ShengFan\ShengFan\qcustomplot.cpp ..\..\..\Photoelectric_Design\QT_ShengFan\ShengFan\mainwindow.ui    

clean: compiler_clean 
	-$(DEL_FILE) debug\main.o debug\mainwindow.o debug\qcustomplot.o debug\qrc_qrc.o debug\moc_mainwindow.o debug\moc_qcustomplot.o
	-$(DEL_FILE) debug\ShengFan_resource_res.o

distclean: clean 
	-$(DEL_FILE) .qmake.stash
	-$(DEL_FILE) $(DESTDIR_TARGET)
	-$(DEL_FILE) Makefile.Debug

mocclean: compiler_moc_header_clean compiler_moc_objc_header_clean compiler_moc_source_clean

mocables: compiler_moc_header_make_all compiler_moc_objc_header_make_all compiler_moc_source_make_all

check: first

benchmark: first

compiler_rcc_make_all: debug/qrc_qrc.cpp
compiler_rcc_clean:
	-$(DEL_FILE) debug\qrc_qrc.cpp
debug/qrc_qrc.cpp: ../../../Photoelectric_Design/QT_ShengFan/ShengFan/qrc.qrc \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/bin/rcc.exe \
		../../../Photoelectric_Design/QT_ShengFan/ShengFan/favicon.ico
	D:\Software\QT5.13.2\5.13.2\mingw73_32\bin\rcc.exe -name qrc ..\..\..\Photoelectric_Design\QT_ShengFan\ShengFan\qrc.qrc -o debug\qrc_qrc.cpp

compiler_qmlcache_make_all:
compiler_qmlcache_clean:
compiler_qmlcache_loader_make_all: debug/qmlcache_loader.cpp
compiler_qmlcache_loader_clean:
	-$(DEL_FILE) debug\qmlcache_loader.cpp
compiler_no_pch_compiler_make_all:
compiler_no_pch_compiler_clean:
compiler_moc_predefs_make_all: debug/moc_predefs.h
compiler_moc_predefs_clean:
	-$(DEL_FILE) debug\moc_predefs.h
debug/moc_predefs.h: D:/Software/QT5.13.2/5.13.2/mingw73_32/mkspecs/features/data/dummy.cpp
	g++ -fno-keep-inline-dllexport -g -std=gnu++11 -Wall -W -Wextra -dM -E -o debug\moc_predefs.h D:\Software\QT5.13.2\5.13.2\mingw73_32\mkspecs\features\data\dummy.cpp

compiler_moc_header_make_all: debug/moc_mainwindow.cpp debug/moc_qcustomplot.cpp
compiler_moc_header_clean:
	-$(DEL_FILE) debug\moc_mainwindow.cpp debug\moc_qcustomplot.cpp
debug/moc_mainwindow.cpp: ../../../Photoelectric_Design/QT_ShengFan/ShengFan/mainwindow.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/QMainWindow \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qmainwindow.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qtwidgetsglobal.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qtguiglobal.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qglobal.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qconfig-bootstrapped.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qconfig.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qtcore-config.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qsystemdetection.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qprocessordetection.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qcompilerdetection.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qtypeinfo.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qsysinfo.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qlogging.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qflags.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qatomic.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qbasicatomic.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qatomic_bootstrap.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qgenericatomic.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qatomic_cxx11.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qatomic_msvc.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qglobalstatic.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qmutex.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qnumeric.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qversiontagging.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qtgui-config.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qtwidgets-config.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qwidget.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qwindowdefs.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qobjectdefs.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qnamespace.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qobjectdefs_impl.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qwindowdefs_win.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qobject.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qstring.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qchar.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qbytearray.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qrefcount.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qarraydata.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qstringliteral.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qstringalgorithms.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qstringview.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qstringbuilder.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qlist.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qalgorithms.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qiterator.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qhashfunctions.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qpair.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qvector.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qpoint.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qbytearraylist.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qstringlist.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qregexp.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qstringmatcher.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qcoreevent.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qscopedpointer.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qmetatype.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qvarlengtharray.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qcontainerfwd.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qobject_impl.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qmargins.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qpaintdevice.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qrect.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qsize.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qpalette.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qcolor.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qrgb.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qrgba64.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qbrush.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qmatrix.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qpolygon.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qregion.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qdatastream.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qiodevice.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qline.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qtransform.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qpainterpath.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qimage.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qpixelformat.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qpixmap.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qsharedpointer.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qshareddata.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qhash.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qsharedpointer_impl.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qfont.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qfontmetrics.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qfontinfo.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qsizepolicy.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qcursor.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qkeysequence.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qevent.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qvariant.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qmap.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qdebug.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qtextstream.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qlocale.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qset.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qcontiguouscache.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qurl.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qurlquery.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qfile.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qfiledevice.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qvector2d.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qtouchdevice.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qtabwidget.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qicon.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtNetwork/QTcpServer \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtNetwork/qtcpserver.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtNetwork/qtnetworkglobal.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtNetwork/qtnetwork-config.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtNetwork/qabstractsocket.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtNetwork/qhostaddress.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtNetwork/QTcpSocket \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtNetwork/qtcpsocket.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/QLabel \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qlabel.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qframe.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/QMessageBox \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qmessagebox.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qdialog.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QString \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QTimer \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qtimer.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qbasictimer.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/QPainter \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qpainter.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qtextoption.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qpen.h \
		../../../Photoelectric_Design/QT_ShengFan/ShengFan/qcustomplot.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QObject \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QPointer \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qpointer.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QSharedPointer \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/QPaintEvent \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/QMouseEvent \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/QWheelEvent \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/QPixmap \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QVector \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QDateTime \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qdatetime.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QMultiMap \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QFlags \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QDebug \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QStack \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qstack.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QCache \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qcache.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QMargins \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qmath.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/QOpenGLContext \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qopenglcontext.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QScopedPointer \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/QSurfaceFormat \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qsurfaceformat.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qopengl.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qt_windows.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtANGLE/GLES3/gl3.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtANGLE/GLES3/gl3platform.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtANGLE/KHR/khrplatform.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtANGLE/GLES2/gl2.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtANGLE/GLES2/gl2platform.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qopengles2ext.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qopenglext.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qopenglversionfunctions.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/QOpenGLFramebufferObject \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qopenglframebufferobject.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/QOffscreenSurface \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qoffscreensurface.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qsurface.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/QWindow \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qwindow.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QEvent \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QRect \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtOpenGL/QGLPixelBuffer \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtOpenGL/qglpixelbuffer.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtOpenGL/qgl.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qpaintengine.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtOpenGL/qglcolormap.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtOpenGL/qtopenglglobal.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QtNumeric \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/QWidget \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtPrintSupport/QtPrintSupport \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtPrintSupport/QtPrintSupportDepends \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QtCore \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QtCoreDepends \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qabstractanimation.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qabstracteventdispatcher.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qeventloop.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qabstractitemmodel.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qabstractnativeeventfilter.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qabstractproxymodel.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qabstractstate.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qabstracttransition.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qanimationgroup.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qarraydataops.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qarraydatapointer.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qbitarray.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qbuffer.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qbytearraymatcher.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qcborarray.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qcborvalue.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qcborcommon.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qregularexpression.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/quuid.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qcbormap.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qcborstream.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qfloat16.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qcollator.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qcommandlineoption.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qcommandlineparser.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qcoreapplication.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qconcatenatetablesproxymodel.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qcryptographichash.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qdeadlinetimer.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qelapsedtimer.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qdir.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qfileinfo.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qdiriterator.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qeasingcurve.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qendian.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qeventtransition.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qexception.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qfactoryinterface.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qfileselector.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QStringList \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qfilesystemwatcher.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qfinalstate.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qfuture.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qfutureinterface.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qrunnable.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qresultstore.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qfuturesynchronizer.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qfuturewatcher.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qhistorystate.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qidentityproxymodel.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qisenum.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qitemselectionmodel.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qjsonarray.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qjsonvalue.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qjsondocument.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qjsonobject.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qlibrary.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qlibraryinfo.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qversionnumber.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qlinkedlist.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qlockfile.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qloggingcategory.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qmessageauthenticationcode.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qmetaobject.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qmimedata.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qmimedatabase.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qmimetype.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qobjectcleanuphandler.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qoperatingsystemversion.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qparallelanimationgroup.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qpauseanimation.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qplugin.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qpluginloader.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qprocess.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qpropertyanimation.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qvariantanimation.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qqueue.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qrandom.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qreadwritelock.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qresource.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qsavefile.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qscopedvaluerollback.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qscopeguard.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qsemaphore.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qsequentialanimationgroup.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qsettings.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qsharedmemory.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qsignalmapper.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qsignaltransition.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qsocketnotifier.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qsortfilterproxymodel.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qstandardpaths.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qstate.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qstatemachine.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qstorageinfo.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qstringlistmodel.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qsystemsemaphore.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qtemporarydir.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qtemporaryfile.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qtextboundaryfinder.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qtextcodec.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qthread.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qthreadpool.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qthreadstorage.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qtimeline.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qtimezone.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qtranslator.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qtransposeproxymodel.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qtypetraits.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qwaitcondition.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qwineventnotifier.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qxmlstream.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qtcoreversion.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/QtGui \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/QtGuiDepends \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qabstracttextdocumentlayout.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qtextlayout.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qtextformat.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qglyphrun.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qrawfont.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qfontdatabase.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qtextcursor.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qtextdocument.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qaccessible.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qaccessiblebridge.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qaccessibleobject.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qaccessibleplugin.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qbackingstore.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qbitmap.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qclipboard.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qdesktopservices.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qdrag.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qgenericmatrix.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qgenericplugin.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qgenericpluginfactory.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qguiapplication.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qinputmethod.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qiconengine.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qiconengineplugin.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qimageiohandler.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qimagereader.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qimagewriter.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qmatrix4x4.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qvector3d.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qvector4d.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qquaternion.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qmovie.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qopenglbuffer.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qopengldebug.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qopenglextrafunctions.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qopenglfunctions.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qopenglpaintdevice.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qopenglpixeltransferoptions.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QSharedDataPointer \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qopenglshaderprogram.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qopengltexture.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qopengltextureblitter.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/QMatrix3x3 \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/QMatrix4x4 \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qopengltimerquery.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qopenglvertexarrayobject.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qopenglwindow.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/QPaintDeviceWindow \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qpaintdevicewindow.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/QPaintDevice \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/QImage \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qpagedpaintdevice.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qpagelayout.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qpagesize.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qpdfwriter.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qpicture.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qpictureformatplugin.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qpixmapcache.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qrasterwindow.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qscreen.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QList \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QSize \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QSizeF \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/QTransform \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qsessionmanager.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qstandarditemmodel.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qstatictext.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qstylehints.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qsyntaxhighlighter.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qtextobject.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qtextdocumentfragment.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qtextdocumentwriter.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qtextlist.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qtexttable.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qvalidator.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qtguiversion.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/QtWidgets \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/QtWidgetsDepends \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qabstractbutton.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qabstractitemdelegate.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qstyleoption.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qabstractspinbox.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qslider.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qabstractslider.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qstyle.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qtabbar.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qrubberband.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qabstractitemview.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qabstractscrollarea.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qaccessiblewidget.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qaction.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qactiongroup.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qapplication.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qdesktopwidget.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qboxlayout.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qlayout.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qlayoutitem.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qgridlayout.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qbuttongroup.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qcalendarwidget.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qcheckbox.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qcolordialog.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qcolormap.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qcolumnview.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qcombobox.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qcommandlinkbutton.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qpushbutton.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qcommonstyle.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qcompleter.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qdatawidgetmapper.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qdatetimeedit.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qdial.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qdialogbuttonbox.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qdirmodel.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qfileiconprovider.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qdockwidget.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qdrawutil.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qerrormessage.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qfiledialog.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qfilesystemmodel.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qfocusframe.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qfontcombobox.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qfontdialog.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qformlayout.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/QLayout \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qgesture.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qgesturerecognizer.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qgraphicsanchorlayout.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qgraphicsitem.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qgraphicslayout.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qgraphicslayoutitem.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qgraphicseffect.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qgraphicsgridlayout.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qgraphicsitemanimation.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qgraphicslinearlayout.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qgraphicsproxywidget.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qgraphicswidget.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qgraphicsscene.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qgraphicssceneevent.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qgraphicstransform.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/QVector3D \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qgraphicsview.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qscrollarea.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qgroupbox.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qheaderview.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qinputdialog.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qlineedit.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qitemdelegate.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qitemeditorfactory.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qkeyeventtransition.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qkeysequenceedit.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qlcdnumber.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qlistview.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qlistwidget.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qmdiarea.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qmdisubwindow.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qmenu.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qmenubar.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qmouseeventtransition.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qopenglwidget.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qplaintextedit.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qtextedit.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qprogressbar.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qprogressdialog.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qproxystyle.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/QCommonStyle \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qradiobutton.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qscrollbar.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qscroller.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QPointF \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/QScrollerProperties \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qscrollerproperties.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QMetaType \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QVariant \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qshortcut.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qsizegrip.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qspinbox.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qsplashscreen.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qsplitter.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qstackedlayout.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qstackedwidget.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qstatusbar.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qstyleditemdelegate.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qstylefactory.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qstylepainter.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qstyleplugin.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qsystemtrayicon.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qtableview.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qtablewidget.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qtextbrowser.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qtoolbar.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qtoolbox.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qtoolbutton.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qtooltip.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qtreeview.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qtreewidget.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qtreewidgetitemiterator.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qundogroup.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qundostack.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qundoview.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qwhatsthis.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qwidgetaction.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qwizard.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qtwidgetsversion.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtPrintSupport/qtprintsupportglobal.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtPrintSupport/qtprintsupport-config.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtPrintSupport/qabstractprintdialog.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtPrintSupport/qpagesetupdialog.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtPrintSupport/qprintdialog.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtPrintSupport/qprintengine.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtPrintSupport/qprinter.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtPrintSupport/qprinterinfo.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QPair \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtPrintSupport/qprintpreviewdialog.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtPrintSupport/qprintpreviewwidget.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtPrintSupport/qtprintsupportversion.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtMqtt/qmqttclient.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtMqtt/qmqttglobal.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtMqtt/qmqttauthenticationproperties.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtMqtt/qmqtttype.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QHash \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtMqtt/qmqttconnectionproperties.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtMqtt/qmqttpublishproperties.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtMqtt/qmqttsubscription.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtMqtt/qmqttmessage.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtMqtt/qmqtttopicname.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QExplicitlySharedDataPointer \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtMqtt/qmqtttopicfilter.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtMqtt/qmqttsubscriptionproperties.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QIODevice \
		../../../Photoelectric_Design/QT_ShengFan/ShengFan/mqtt/qmqtt.h \
		../../../Photoelectric_Design/QT_ShengFan/ShengFan/mqtt/qmqtt_message.h \
		../../../Photoelectric_Design/QT_ShengFan/ShengFan/mqtt/qmqtt_global.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QtGlobal \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QByteArray \
		../../../Photoelectric_Design/QT_ShengFan/ShengFan/mqtt/qmqtt_client.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtNetwork/QHostAddress \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtNetwork/QAbstractSocket \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/QList \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtNetwork/QSslConfiguration \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtNetwork/qsslconfiguration.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtNetwork/qsslsocket.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtNetwork/qsslerror.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtNetwork/qsslcertificate.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtNetwork/qssl.h \
		debug/moc_predefs.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/bin/moc.exe
	D:\Software\QT5.13.2\5.13.2\mingw73_32\bin\moc.exe $(DEFINES) --include E:/Competition/shengfan/QT_ShengFan/build-ShengFan-Desktop_Qt_5_13_2_MinGW_32_bit-Release/debug/moc_predefs.h -ID:/Software/QT5.13.2/5.13.2/mingw73_32/mkspecs/win32-g++ -IE:/Competition/Photoelectric_Design/QT_ShengFan/ShengFan -IE:/Competition/Photoelectric_Design/QT_ShengFan/ShengFan/include -IE:/Competition/Photoelectric_Design/QT_ShengFan/ShengFan/include -IE:/Competition/Photoelectric_Design/QT_ShengFan/ShengFan/include -IE:/Competition/Photoelectric_Design/QT_ShengFan/ShengFan/include -IE:/Competition/Photoelectric_Design/QT_ShengFan/ShengFan/mqtt -ID:/Software/QT5.13.2/5.13.2/mingw73_32/include -ID:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtPrintSupport -ID:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets -ID:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui -ID:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtANGLE -ID:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtNetwork -ID:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore -I. -ID:/Software/QT5.13.2/Tools/mingw730_32/lib/gcc/i686-w64-mingw32/7.3.0/include/c++ -ID:/Software/QT5.13.2/Tools/mingw730_32/lib/gcc/i686-w64-mingw32/7.3.0/include/c++/i686-w64-mingw32 -ID:/Software/QT5.13.2/Tools/mingw730_32/lib/gcc/i686-w64-mingw32/7.3.0/include/c++/backward -ID:/Software/QT5.13.2/Tools/mingw730_32/lib/gcc/i686-w64-mingw32/7.3.0/include -ID:/Software/QT5.13.2/Tools/mingw730_32/lib/gcc/i686-w64-mingw32/7.3.0/include-fixed -ID:/Software/QT5.13.2/Tools/mingw730_32/i686-w64-mingw32/include ..\..\..\Photoelectric_Design\QT_ShengFan\ShengFan\mainwindow.h -o debug\moc_mainwindow.cpp

debug/moc_qcustomplot.cpp: ../../../Photoelectric_Design/QT_ShengFan/ShengFan/qcustomplot.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qglobal.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qconfig-bootstrapped.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qconfig.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qtcore-config.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qsystemdetection.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qprocessordetection.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qcompilerdetection.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qtypeinfo.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qsysinfo.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qlogging.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qflags.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qatomic.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qbasicatomic.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qatomic_bootstrap.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qgenericatomic.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qatomic_cxx11.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qatomic_msvc.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qglobalstatic.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qmutex.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qnumeric.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qversiontagging.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QObject \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qobject.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qobjectdefs.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qnamespace.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qobjectdefs_impl.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qstring.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qchar.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qbytearray.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qrefcount.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qarraydata.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qstringliteral.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qstringalgorithms.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qstringview.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qstringbuilder.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qlist.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qalgorithms.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qiterator.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qhashfunctions.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qpair.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qvector.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qpoint.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qbytearraylist.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qstringlist.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qregexp.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qstringmatcher.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qcoreevent.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qscopedpointer.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qmetatype.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qvarlengtharray.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qcontainerfwd.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qobject_impl.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QPointer \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qpointer.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qsharedpointer.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qshareddata.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qhash.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qsharedpointer_impl.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QSharedPointer \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QTimer \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qtimer.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qbasictimer.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/QPainter \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qpainter.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qtguiglobal.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qtgui-config.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qrect.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qmargins.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qsize.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qpixmap.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qpaintdevice.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qwindowdefs.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qwindowdefs_win.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qcolor.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qrgb.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qrgba64.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qimage.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qpixelformat.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qtransform.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qmatrix.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qpolygon.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qregion.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qdatastream.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qiodevice.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qline.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qpainterpath.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qtextoption.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qpen.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qbrush.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qfontinfo.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qfont.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qfontmetrics.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/QPaintEvent \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qevent.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qkeysequence.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qvariant.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qmap.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qdebug.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qtextstream.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qlocale.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qset.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qcontiguouscache.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qurl.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qurlquery.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qfile.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qfiledevice.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qvector2d.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qtouchdevice.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/QMouseEvent \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/QWheelEvent \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/QPixmap \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QVector \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QString \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QDateTime \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qdatetime.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QMultiMap \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QFlags \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QDebug \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QStack \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qstack.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QCache \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qcache.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QMargins \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qmath.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/QOpenGLContext \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qopenglcontext.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QScopedPointer \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/QSurfaceFormat \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qsurfaceformat.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qopengl.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qt_windows.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtANGLE/GLES3/gl3.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtANGLE/GLES3/gl3platform.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtANGLE/KHR/khrplatform.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtANGLE/GLES2/gl2.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtANGLE/GLES2/gl2platform.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qopengles2ext.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qopenglext.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qopenglversionfunctions.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/QOpenGLFramebufferObject \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qopenglframebufferobject.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/QOffscreenSurface \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qoffscreensurface.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qsurface.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/QWindow \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qwindow.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QEvent \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QRect \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qicon.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qcursor.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtOpenGL/QGLPixelBuffer \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtOpenGL/qglpixelbuffer.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtOpenGL/qgl.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qwidget.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qtwidgetsglobal.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qtwidgets-config.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qpalette.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qsizepolicy.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qpaintengine.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtOpenGL/qglcolormap.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtOpenGL/qtopenglglobal.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QtNumeric \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/QWidget \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtPrintSupport/QtPrintSupport \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtPrintSupport/QtPrintSupportDepends \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QtCore \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QtCoreDepends \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qabstractanimation.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qabstracteventdispatcher.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qeventloop.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qabstractitemmodel.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qabstractnativeeventfilter.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qabstractproxymodel.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qabstractstate.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qabstracttransition.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qanimationgroup.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qarraydataops.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qarraydatapointer.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qbitarray.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qbuffer.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qbytearraymatcher.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qcborarray.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qcborvalue.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qcborcommon.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qregularexpression.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/quuid.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qcbormap.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qcborstream.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qfloat16.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qcollator.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qcommandlineoption.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qcommandlineparser.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qcoreapplication.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qconcatenatetablesproxymodel.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qcryptographichash.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qdeadlinetimer.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qelapsedtimer.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qdir.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qfileinfo.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qdiriterator.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qeasingcurve.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qendian.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qeventtransition.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qexception.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qfactoryinterface.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qfileselector.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QStringList \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qfilesystemwatcher.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qfinalstate.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qfuture.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qfutureinterface.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qrunnable.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qresultstore.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qfuturesynchronizer.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qfuturewatcher.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qhistorystate.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qidentityproxymodel.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qisenum.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qitemselectionmodel.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qjsonarray.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qjsonvalue.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qjsondocument.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qjsonobject.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qlibrary.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qlibraryinfo.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qversionnumber.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qlinkedlist.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qlockfile.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qloggingcategory.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qmessageauthenticationcode.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qmetaobject.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qmimedata.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qmimedatabase.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qmimetype.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qobjectcleanuphandler.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qoperatingsystemversion.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qparallelanimationgroup.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qpauseanimation.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qplugin.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qpluginloader.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qprocess.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qpropertyanimation.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qvariantanimation.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qqueue.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qrandom.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qreadwritelock.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qresource.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qsavefile.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qscopedvaluerollback.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qscopeguard.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qsemaphore.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qsequentialanimationgroup.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qsettings.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qsharedmemory.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qsignalmapper.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qsignaltransition.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qsocketnotifier.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qsortfilterproxymodel.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qstandardpaths.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qstate.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qstatemachine.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qstorageinfo.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qstringlistmodel.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qsystemsemaphore.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qtemporarydir.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qtemporaryfile.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qtextboundaryfinder.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qtextcodec.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qthread.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qthreadpool.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qthreadstorage.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qtimeline.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qtimezone.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qtranslator.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qtransposeproxymodel.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qtypetraits.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qwaitcondition.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qwineventnotifier.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qxmlstream.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qtcoreversion.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/QtGui \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/QtGuiDepends \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qabstracttextdocumentlayout.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qtextlayout.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qtextformat.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qglyphrun.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qrawfont.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qfontdatabase.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qtextcursor.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qtextdocument.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qaccessible.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qaccessiblebridge.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qaccessibleobject.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qaccessibleplugin.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qbackingstore.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qbitmap.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qclipboard.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qdesktopservices.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qdrag.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qgenericmatrix.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qgenericplugin.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qgenericpluginfactory.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qguiapplication.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qinputmethod.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qiconengine.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qiconengineplugin.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qimageiohandler.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qimagereader.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qimagewriter.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qmatrix4x4.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qvector3d.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qvector4d.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qquaternion.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qmovie.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qopenglbuffer.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qopengldebug.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qopenglextrafunctions.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qopenglfunctions.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qopenglpaintdevice.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qopenglpixeltransferoptions.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QSharedDataPointer \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qopenglshaderprogram.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qopengltexture.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qopengltextureblitter.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/QMatrix3x3 \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/QMatrix4x4 \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qopengltimerquery.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qopenglvertexarrayobject.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qopenglwindow.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/QPaintDeviceWindow \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qpaintdevicewindow.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/QPaintDevice \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/QImage \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qpagedpaintdevice.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qpagelayout.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qpagesize.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qpdfwriter.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qpicture.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qpictureformatplugin.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qpixmapcache.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qrasterwindow.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qscreen.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QList \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QSize \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QSizeF \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/QTransform \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qsessionmanager.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qstandarditemmodel.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qstatictext.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qstylehints.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qsyntaxhighlighter.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qtextobject.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qtextdocumentfragment.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qtextdocumentwriter.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qtextlist.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qtexttable.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qvalidator.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qtguiversion.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/QtWidgets \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/QtWidgetsDepends \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qabstractbutton.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qabstractitemdelegate.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qstyleoption.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qabstractspinbox.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qslider.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qabstractslider.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qstyle.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qtabbar.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qtabwidget.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qrubberband.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qframe.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qabstractitemview.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qabstractscrollarea.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qaccessiblewidget.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qaction.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qactiongroup.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qapplication.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qdesktopwidget.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qboxlayout.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qlayout.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qlayoutitem.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qgridlayout.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qbuttongroup.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qcalendarwidget.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qcheckbox.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qcolordialog.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qdialog.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qcolormap.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qcolumnview.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qcombobox.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qcommandlinkbutton.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qpushbutton.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qcommonstyle.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qcompleter.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qdatawidgetmapper.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qdatetimeedit.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qdial.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qdialogbuttonbox.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qdirmodel.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qfileiconprovider.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qdockwidget.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qdrawutil.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qerrormessage.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qfiledialog.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qfilesystemmodel.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qfocusframe.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qfontcombobox.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qfontdialog.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qformlayout.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/QLayout \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qgesture.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qgesturerecognizer.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qgraphicsanchorlayout.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qgraphicsitem.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qgraphicslayout.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qgraphicslayoutitem.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qgraphicseffect.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qgraphicsgridlayout.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qgraphicsitemanimation.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qgraphicslinearlayout.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qgraphicsproxywidget.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qgraphicswidget.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qgraphicsscene.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qgraphicssceneevent.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qgraphicstransform.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/QVector3D \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qgraphicsview.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qscrollarea.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qgroupbox.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qheaderview.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qinputdialog.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qlineedit.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qitemdelegate.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qitemeditorfactory.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qkeyeventtransition.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qkeysequenceedit.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qlabel.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qlcdnumber.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qlistview.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qlistwidget.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qmainwindow.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qmdiarea.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qmdisubwindow.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qmenu.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qmenubar.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qmessagebox.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qmouseeventtransition.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qopenglwidget.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qplaintextedit.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qtextedit.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qprogressbar.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qprogressdialog.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qproxystyle.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/QCommonStyle \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qradiobutton.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qscrollbar.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qscroller.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QPointF \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/QScrollerProperties \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qscrollerproperties.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QMetaType \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QVariant \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qshortcut.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qsizegrip.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qspinbox.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qsplashscreen.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qsplitter.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qstackedlayout.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qstackedwidget.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qstatusbar.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qstyleditemdelegate.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qstylefactory.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qstylepainter.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qstyleplugin.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qsystemtrayicon.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qtableview.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qtablewidget.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qtextbrowser.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qtoolbar.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qtoolbox.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qtoolbutton.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qtooltip.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qtreeview.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qtreewidget.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qtreewidgetitemiterator.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qundogroup.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qundostack.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qundoview.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qwhatsthis.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qwidgetaction.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qwizard.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qtwidgetsversion.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtPrintSupport/qtprintsupportglobal.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtPrintSupport/qtprintsupport-config.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtPrintSupport/qabstractprintdialog.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtPrintSupport/qpagesetupdialog.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtPrintSupport/qprintdialog.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtPrintSupport/qprintengine.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtPrintSupport/qprinter.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtPrintSupport/qprinterinfo.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QPair \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtPrintSupport/qprintpreviewdialog.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtPrintSupport/qprintpreviewwidget.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtPrintSupport/qtprintsupportversion.h \
		debug/moc_predefs.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/bin/moc.exe
	D:\Software\QT5.13.2\5.13.2\mingw73_32\bin\moc.exe $(DEFINES) --include E:/Competition/shengfan/QT_ShengFan/build-ShengFan-Desktop_Qt_5_13_2_MinGW_32_bit-Release/debug/moc_predefs.h -ID:/Software/QT5.13.2/5.13.2/mingw73_32/mkspecs/win32-g++ -IE:/Competition/Photoelectric_Design/QT_ShengFan/ShengFan -IE:/Competition/Photoelectric_Design/QT_ShengFan/ShengFan/include -IE:/Competition/Photoelectric_Design/QT_ShengFan/ShengFan/include -IE:/Competition/Photoelectric_Design/QT_ShengFan/ShengFan/include -IE:/Competition/Photoelectric_Design/QT_ShengFan/ShengFan/include -IE:/Competition/Photoelectric_Design/QT_ShengFan/ShengFan/mqtt -ID:/Software/QT5.13.2/5.13.2/mingw73_32/include -ID:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtPrintSupport -ID:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets -ID:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui -ID:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtANGLE -ID:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtNetwork -ID:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore -I. -ID:/Software/QT5.13.2/Tools/mingw730_32/lib/gcc/i686-w64-mingw32/7.3.0/include/c++ -ID:/Software/QT5.13.2/Tools/mingw730_32/lib/gcc/i686-w64-mingw32/7.3.0/include/c++/i686-w64-mingw32 -ID:/Software/QT5.13.2/Tools/mingw730_32/lib/gcc/i686-w64-mingw32/7.3.0/include/c++/backward -ID:/Software/QT5.13.2/Tools/mingw730_32/lib/gcc/i686-w64-mingw32/7.3.0/include -ID:/Software/QT5.13.2/Tools/mingw730_32/lib/gcc/i686-w64-mingw32/7.3.0/include-fixed -ID:/Software/QT5.13.2/Tools/mingw730_32/i686-w64-mingw32/include ..\..\..\Photoelectric_Design\QT_ShengFan\ShengFan\qcustomplot.h -o debug\moc_qcustomplot.cpp

compiler_moc_objc_header_make_all:
compiler_moc_objc_header_clean:
compiler_moc_source_make_all:
compiler_moc_source_clean:
compiler_uic_make_all: ui_mainwindow.h
compiler_uic_clean:
	-$(DEL_FILE) ui_mainwindow.h
ui_mainwindow.h: ../../../Photoelectric_Design/QT_ShengFan/ShengFan/mainwindow.ui \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/bin/uic.exe \
		../../../Photoelectric_Design/QT_ShengFan/ShengFan/qcustomplot.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qglobal.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qconfig-bootstrapped.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qconfig.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qtcore-config.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qsystemdetection.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qprocessordetection.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qcompilerdetection.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qtypeinfo.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qsysinfo.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qlogging.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qflags.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qatomic.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qbasicatomic.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qatomic_bootstrap.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qgenericatomic.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qatomic_cxx11.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qatomic_msvc.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qglobalstatic.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qmutex.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qnumeric.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qversiontagging.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QObject \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qobject.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qobjectdefs.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qnamespace.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qobjectdefs_impl.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qstring.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qchar.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qbytearray.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qrefcount.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qarraydata.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qstringliteral.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qstringalgorithms.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qstringview.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qstringbuilder.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qlist.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qalgorithms.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qiterator.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qhashfunctions.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qpair.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qvector.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qpoint.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qbytearraylist.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qstringlist.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qregexp.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qstringmatcher.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qcoreevent.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qscopedpointer.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qmetatype.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qvarlengtharray.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qcontainerfwd.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qobject_impl.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QPointer \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qpointer.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qsharedpointer.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qshareddata.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qhash.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qsharedpointer_impl.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QSharedPointer \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QTimer \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qtimer.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qbasictimer.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/QPainter \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qpainter.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qtguiglobal.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qtgui-config.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qrect.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qmargins.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qsize.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qpixmap.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qpaintdevice.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qwindowdefs.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qwindowdefs_win.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qcolor.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qrgb.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qrgba64.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qimage.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qpixelformat.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qtransform.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qmatrix.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qpolygon.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qregion.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qdatastream.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qiodevice.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qline.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qpainterpath.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qtextoption.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qpen.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qbrush.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qfontinfo.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qfont.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qfontmetrics.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/QPaintEvent \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qevent.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qkeysequence.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qvariant.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qmap.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qdebug.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qtextstream.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qlocale.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qset.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qcontiguouscache.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qurl.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qurlquery.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qfile.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qfiledevice.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qvector2d.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qtouchdevice.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/QMouseEvent \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/QWheelEvent \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/QPixmap \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QVector \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QString \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QDateTime \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qdatetime.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QMultiMap \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QFlags \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QDebug \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QStack \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qstack.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QCache \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qcache.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QMargins \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qmath.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/QOpenGLContext \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qopenglcontext.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QScopedPointer \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/QSurfaceFormat \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qsurfaceformat.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qopengl.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qt_windows.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtANGLE/GLES3/gl3.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtANGLE/GLES3/gl3platform.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtANGLE/KHR/khrplatform.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtANGLE/GLES2/gl2.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtANGLE/GLES2/gl2platform.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qopengles2ext.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qopenglext.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qopenglversionfunctions.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/QOpenGLFramebufferObject \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qopenglframebufferobject.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/QOffscreenSurface \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qoffscreensurface.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qsurface.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/QWindow \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qwindow.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QEvent \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QRect \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qicon.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qcursor.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtOpenGL/QGLPixelBuffer \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtOpenGL/qglpixelbuffer.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtOpenGL/qgl.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qwidget.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qtwidgetsglobal.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qtwidgets-config.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qpalette.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qsizepolicy.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qpaintengine.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtOpenGL/qglcolormap.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtOpenGL/qtopenglglobal.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QtNumeric \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/QWidget \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtPrintSupport/QtPrintSupport \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtPrintSupport/QtPrintSupportDepends \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QtCore \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QtCoreDepends \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qabstractanimation.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qabstracteventdispatcher.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qeventloop.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qabstractitemmodel.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qabstractnativeeventfilter.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qabstractproxymodel.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qabstractstate.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qabstracttransition.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qanimationgroup.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qarraydataops.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qarraydatapointer.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qbitarray.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qbuffer.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qbytearraymatcher.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qcborarray.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qcborvalue.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qcborcommon.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qregularexpression.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/quuid.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qcbormap.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qcborstream.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qfloat16.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qcollator.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qcommandlineoption.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qcommandlineparser.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qcoreapplication.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qconcatenatetablesproxymodel.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qcryptographichash.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qdeadlinetimer.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qelapsedtimer.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qdir.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qfileinfo.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qdiriterator.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qeasingcurve.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qendian.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qeventtransition.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qexception.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qfactoryinterface.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qfileselector.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QStringList \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qfilesystemwatcher.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qfinalstate.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qfuture.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qfutureinterface.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qrunnable.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qresultstore.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qfuturesynchronizer.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qfuturewatcher.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qhistorystate.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qidentityproxymodel.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qisenum.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qitemselectionmodel.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qjsonarray.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qjsonvalue.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qjsondocument.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qjsonobject.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qlibrary.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qlibraryinfo.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qversionnumber.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qlinkedlist.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qlockfile.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qloggingcategory.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qmessageauthenticationcode.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qmetaobject.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qmimedata.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qmimedatabase.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qmimetype.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qobjectcleanuphandler.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qoperatingsystemversion.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qparallelanimationgroup.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qpauseanimation.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qplugin.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qpluginloader.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qprocess.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qpropertyanimation.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qvariantanimation.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qqueue.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qrandom.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qreadwritelock.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qresource.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qsavefile.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qscopedvaluerollback.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qscopeguard.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qsemaphore.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qsequentialanimationgroup.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qsettings.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qsharedmemory.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qsignalmapper.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qsignaltransition.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qsocketnotifier.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qsortfilterproxymodel.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qstandardpaths.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qstate.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qstatemachine.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qstorageinfo.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qstringlistmodel.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qsystemsemaphore.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qtemporarydir.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qtemporaryfile.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qtextboundaryfinder.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qtextcodec.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qthread.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qthreadpool.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qthreadstorage.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qtimeline.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qtimezone.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qtranslator.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qtransposeproxymodel.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qtypetraits.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qwaitcondition.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qwineventnotifier.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qxmlstream.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qtcoreversion.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/QtGui \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/QtGuiDepends \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qabstracttextdocumentlayout.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qtextlayout.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qtextformat.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qglyphrun.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qrawfont.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qfontdatabase.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qtextcursor.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qtextdocument.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qaccessible.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qaccessiblebridge.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qaccessibleobject.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qaccessibleplugin.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qbackingstore.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qbitmap.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qclipboard.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qdesktopservices.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qdrag.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qgenericmatrix.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qgenericplugin.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qgenericpluginfactory.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qguiapplication.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qinputmethod.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qiconengine.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qiconengineplugin.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qimageiohandler.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qimagereader.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qimagewriter.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qmatrix4x4.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qvector3d.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qvector4d.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qquaternion.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qmovie.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qopenglbuffer.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qopengldebug.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qopenglextrafunctions.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qopenglfunctions.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qopenglpaintdevice.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qopenglpixeltransferoptions.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QSharedDataPointer \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qopenglshaderprogram.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qopengltexture.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qopengltextureblitter.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/QMatrix3x3 \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/QMatrix4x4 \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qopengltimerquery.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qopenglvertexarrayobject.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qopenglwindow.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/QPaintDeviceWindow \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qpaintdevicewindow.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/QPaintDevice \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/QImage \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qpagedpaintdevice.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qpagelayout.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qpagesize.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qpdfwriter.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qpicture.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qpictureformatplugin.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qpixmapcache.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qrasterwindow.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qscreen.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QList \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QSize \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QSizeF \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/QTransform \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qsessionmanager.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qstandarditemmodel.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qstatictext.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qstylehints.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qsyntaxhighlighter.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qtextobject.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qtextdocumentfragment.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qtextdocumentwriter.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qtextlist.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qtexttable.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qvalidator.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qtguiversion.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/QtWidgets \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/QtWidgetsDepends \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qabstractbutton.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qabstractitemdelegate.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qstyleoption.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qabstractspinbox.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qslider.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qabstractslider.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qstyle.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qtabbar.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qtabwidget.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qrubberband.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qframe.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qabstractitemview.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qabstractscrollarea.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qaccessiblewidget.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qaction.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qactiongroup.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qapplication.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qdesktopwidget.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qboxlayout.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qlayout.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qlayoutitem.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qgridlayout.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qbuttongroup.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qcalendarwidget.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qcheckbox.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qcolordialog.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qdialog.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qcolormap.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qcolumnview.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qcombobox.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qcommandlinkbutton.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qpushbutton.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qcommonstyle.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qcompleter.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qdatawidgetmapper.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qdatetimeedit.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qdial.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qdialogbuttonbox.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qdirmodel.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qfileiconprovider.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qdockwidget.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qdrawutil.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qerrormessage.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qfiledialog.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qfilesystemmodel.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qfocusframe.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qfontcombobox.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qfontdialog.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qformlayout.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/QLayout \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qgesture.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qgesturerecognizer.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qgraphicsanchorlayout.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qgraphicsitem.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qgraphicslayout.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qgraphicslayoutitem.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qgraphicseffect.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qgraphicsgridlayout.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qgraphicsitemanimation.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qgraphicslinearlayout.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qgraphicsproxywidget.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qgraphicswidget.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qgraphicsscene.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qgraphicssceneevent.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qgraphicstransform.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/QVector3D \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qgraphicsview.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qscrollarea.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qgroupbox.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qheaderview.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qinputdialog.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qlineedit.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qitemdelegate.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qitemeditorfactory.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qkeyeventtransition.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qkeysequenceedit.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qlabel.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qlcdnumber.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qlistview.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qlistwidget.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qmainwindow.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qmdiarea.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qmdisubwindow.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qmenu.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qmenubar.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qmessagebox.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qmouseeventtransition.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qopenglwidget.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qplaintextedit.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qtextedit.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qprogressbar.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qprogressdialog.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qproxystyle.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/QCommonStyle \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qradiobutton.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qscrollbar.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qscroller.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QPointF \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/QScrollerProperties \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qscrollerproperties.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QMetaType \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QVariant \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qshortcut.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qsizegrip.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qspinbox.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qsplashscreen.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qsplitter.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qstackedlayout.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qstackedwidget.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qstatusbar.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qstyleditemdelegate.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qstylefactory.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qstylepainter.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qstyleplugin.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qsystemtrayicon.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qtableview.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qtablewidget.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qtextbrowser.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qtoolbar.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qtoolbox.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qtoolbutton.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qtooltip.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qtreeview.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qtreewidget.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qtreewidgetitemiterator.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qundogroup.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qundostack.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qundoview.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qwhatsthis.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qwidgetaction.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qwizard.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qtwidgetsversion.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtPrintSupport/qtprintsupportglobal.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtPrintSupport/qtprintsupport-config.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtPrintSupport/qabstractprintdialog.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtPrintSupport/qpagesetupdialog.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtPrintSupport/qprintdialog.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtPrintSupport/qprintengine.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtPrintSupport/qprinter.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtPrintSupport/qprinterinfo.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QPair \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtPrintSupport/qprintpreviewdialog.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtPrintSupport/qprintpreviewwidget.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtPrintSupport/qtprintsupportversion.h
	D:\Software\QT5.13.2\5.13.2\mingw73_32\bin\uic.exe ..\..\..\Photoelectric_Design\QT_ShengFan\ShengFan\mainwindow.ui -o ui_mainwindow.h

compiler_yacc_decl_make_all:
compiler_yacc_decl_clean:
compiler_yacc_impl_make_all:
compiler_yacc_impl_clean:
compiler_lex_make_all:
compiler_lex_clean:
compiler_clean: compiler_rcc_clean compiler_moc_predefs_clean compiler_moc_header_clean compiler_uic_clean 



####### Compile

debug/main.o: ../../../Photoelectric_Design/QT_ShengFan/ShengFan/main.cpp ../../../Photoelectric_Design/QT_ShengFan/ShengFan/mainwindow.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/QMainWindow \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qmainwindow.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qtwidgetsglobal.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qtguiglobal.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qglobal.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qconfig-bootstrapped.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qconfig.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qtcore-config.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qsystemdetection.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qprocessordetection.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qcompilerdetection.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qtypeinfo.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qsysinfo.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qlogging.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qflags.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qatomic.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qbasicatomic.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qatomic_bootstrap.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qgenericatomic.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qatomic_cxx11.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qatomic_msvc.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qglobalstatic.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qmutex.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qnumeric.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qversiontagging.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qtgui-config.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qtwidgets-config.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qwidget.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qwindowdefs.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qobjectdefs.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qnamespace.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qobjectdefs_impl.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qwindowdefs_win.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qobject.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qstring.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qchar.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qbytearray.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qrefcount.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qarraydata.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qstringliteral.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qstringalgorithms.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qstringview.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qstringbuilder.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qlist.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qalgorithms.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qiterator.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qhashfunctions.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qpair.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qvector.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qpoint.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qbytearraylist.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qstringlist.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qregexp.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qstringmatcher.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qcoreevent.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qscopedpointer.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qmetatype.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qvarlengtharray.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qcontainerfwd.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qobject_impl.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qmargins.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qpaintdevice.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qrect.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qsize.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qpalette.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qcolor.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qrgb.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qrgba64.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qbrush.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qmatrix.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qpolygon.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qregion.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qdatastream.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qiodevice.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qline.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qtransform.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qpainterpath.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qimage.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qpixelformat.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qpixmap.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qsharedpointer.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qshareddata.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qhash.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qsharedpointer_impl.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qfont.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qfontmetrics.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qfontinfo.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qsizepolicy.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qcursor.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qkeysequence.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qevent.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qvariant.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qmap.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qdebug.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qtextstream.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qlocale.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qset.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qcontiguouscache.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qurl.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qurlquery.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qfile.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qfiledevice.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qvector2d.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qtouchdevice.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qtabwidget.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qicon.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtNetwork/QTcpServer \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtNetwork/qtcpserver.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtNetwork/qtnetworkglobal.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtNetwork/qtnetwork-config.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtNetwork/qabstractsocket.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtNetwork/qhostaddress.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtNetwork/QTcpSocket \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtNetwork/qtcpsocket.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/QLabel \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qlabel.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qframe.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/QMessageBox \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qmessagebox.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qdialog.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QString \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QTimer \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qtimer.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qbasictimer.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/QPainter \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qpainter.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qtextoption.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qpen.h \
		../../../Photoelectric_Design/QT_ShengFan/ShengFan/qcustomplot.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QObject \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QPointer \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qpointer.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QSharedPointer \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/QPaintEvent \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/QMouseEvent \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/QWheelEvent \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/QPixmap \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QVector \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QDateTime \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qdatetime.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QMultiMap \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QFlags \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QDebug \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QStack \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qstack.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QCache \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qcache.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QMargins \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qmath.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/QOpenGLContext \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qopenglcontext.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QScopedPointer \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/QSurfaceFormat \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qsurfaceformat.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qopengl.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qt_windows.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtANGLE/GLES3/gl3.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtANGLE/GLES3/gl3platform.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtANGLE/KHR/khrplatform.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtANGLE/GLES2/gl2.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtANGLE/GLES2/gl2platform.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qopengles2ext.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qopenglext.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qopenglversionfunctions.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/QOpenGLFramebufferObject \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qopenglframebufferobject.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/QOffscreenSurface \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qoffscreensurface.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qsurface.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/QWindow \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qwindow.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QEvent \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QRect \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtOpenGL/QGLPixelBuffer \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtOpenGL/qglpixelbuffer.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtOpenGL/qgl.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qpaintengine.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtOpenGL/qglcolormap.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtOpenGL/qtopenglglobal.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QtNumeric \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/QWidget \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtPrintSupport/QtPrintSupport \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtPrintSupport/QtPrintSupportDepends \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QtCore \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QtCoreDepends \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qabstractanimation.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qabstracteventdispatcher.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qeventloop.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qabstractitemmodel.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qabstractnativeeventfilter.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qabstractproxymodel.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qabstractstate.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qabstracttransition.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qanimationgroup.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qarraydataops.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qarraydatapointer.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qbitarray.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qbuffer.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qbytearraymatcher.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qcborarray.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qcborvalue.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qcborcommon.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qregularexpression.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/quuid.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qcbormap.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qcborstream.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qfloat16.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qcollator.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qcommandlineoption.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qcommandlineparser.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qcoreapplication.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qconcatenatetablesproxymodel.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qcryptographichash.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qdeadlinetimer.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qelapsedtimer.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qdir.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qfileinfo.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qdiriterator.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qeasingcurve.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qendian.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qeventtransition.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qexception.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qfactoryinterface.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qfileselector.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QStringList \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qfilesystemwatcher.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qfinalstate.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qfuture.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qfutureinterface.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qrunnable.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qresultstore.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qfuturesynchronizer.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qfuturewatcher.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qhistorystate.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qidentityproxymodel.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qisenum.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qitemselectionmodel.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qjsonarray.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qjsonvalue.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qjsondocument.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qjsonobject.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qlibrary.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qlibraryinfo.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qversionnumber.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qlinkedlist.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qlockfile.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qloggingcategory.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qmessageauthenticationcode.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qmetaobject.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qmimedata.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qmimedatabase.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qmimetype.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qobjectcleanuphandler.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qoperatingsystemversion.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qparallelanimationgroup.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qpauseanimation.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qplugin.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qpluginloader.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qprocess.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qpropertyanimation.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qvariantanimation.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qqueue.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qrandom.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qreadwritelock.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qresource.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qsavefile.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qscopedvaluerollback.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qscopeguard.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qsemaphore.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qsequentialanimationgroup.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qsettings.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qsharedmemory.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qsignalmapper.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qsignaltransition.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qsocketnotifier.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qsortfilterproxymodel.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qstandardpaths.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qstate.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qstatemachine.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qstorageinfo.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qstringlistmodel.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qsystemsemaphore.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qtemporarydir.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qtemporaryfile.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qtextboundaryfinder.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qtextcodec.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qthread.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qthreadpool.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qthreadstorage.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qtimeline.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qtimezone.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qtranslator.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qtransposeproxymodel.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qtypetraits.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qwaitcondition.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qwineventnotifier.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qxmlstream.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qtcoreversion.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/QtGui \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/QtGuiDepends \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qabstracttextdocumentlayout.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qtextlayout.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qtextformat.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qglyphrun.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qrawfont.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qfontdatabase.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qtextcursor.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qtextdocument.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qaccessible.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qaccessiblebridge.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qaccessibleobject.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qaccessibleplugin.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qbackingstore.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qbitmap.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qclipboard.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qdesktopservices.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qdrag.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qgenericmatrix.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qgenericplugin.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qgenericpluginfactory.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qguiapplication.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qinputmethod.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qiconengine.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qiconengineplugin.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qimageiohandler.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qimagereader.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qimagewriter.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qmatrix4x4.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qvector3d.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qvector4d.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qquaternion.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qmovie.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qopenglbuffer.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qopengldebug.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qopenglextrafunctions.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qopenglfunctions.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qopenglpaintdevice.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qopenglpixeltransferoptions.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QSharedDataPointer \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qopenglshaderprogram.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qopengltexture.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qopengltextureblitter.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/QMatrix3x3 \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/QMatrix4x4 \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qopengltimerquery.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qopenglvertexarrayobject.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qopenglwindow.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/QPaintDeviceWindow \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qpaintdevicewindow.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/QPaintDevice \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/QImage \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qpagedpaintdevice.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qpagelayout.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qpagesize.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qpdfwriter.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qpicture.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qpictureformatplugin.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qpixmapcache.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qrasterwindow.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qscreen.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QList \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QSize \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QSizeF \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/QTransform \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qsessionmanager.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qstandarditemmodel.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qstatictext.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qstylehints.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qsyntaxhighlighter.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qtextobject.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qtextdocumentfragment.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qtextdocumentwriter.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qtextlist.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qtexttable.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qvalidator.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qtguiversion.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/QtWidgets \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/QtWidgetsDepends \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qabstractbutton.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qabstractitemdelegate.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qstyleoption.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qabstractspinbox.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qslider.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qabstractslider.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qstyle.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qtabbar.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qrubberband.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qabstractitemview.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qabstractscrollarea.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qaccessiblewidget.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qaction.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qactiongroup.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qapplication.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qdesktopwidget.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qboxlayout.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qlayout.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qlayoutitem.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qgridlayout.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qbuttongroup.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qcalendarwidget.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qcheckbox.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qcolordialog.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qcolormap.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qcolumnview.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qcombobox.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qcommandlinkbutton.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qpushbutton.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qcommonstyle.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qcompleter.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qdatawidgetmapper.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qdatetimeedit.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qdial.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qdialogbuttonbox.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qdirmodel.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qfileiconprovider.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qdockwidget.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qdrawutil.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qerrormessage.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qfiledialog.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qfilesystemmodel.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qfocusframe.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qfontcombobox.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qfontdialog.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qformlayout.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/QLayout \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qgesture.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qgesturerecognizer.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qgraphicsanchorlayout.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qgraphicsitem.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qgraphicslayout.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qgraphicslayoutitem.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qgraphicseffect.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qgraphicsgridlayout.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qgraphicsitemanimation.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qgraphicslinearlayout.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qgraphicsproxywidget.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qgraphicswidget.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qgraphicsscene.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qgraphicssceneevent.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qgraphicstransform.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/QVector3D \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qgraphicsview.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qscrollarea.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qgroupbox.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qheaderview.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qinputdialog.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qlineedit.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qitemdelegate.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qitemeditorfactory.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qkeyeventtransition.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qkeysequenceedit.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qlcdnumber.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qlistview.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qlistwidget.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qmdiarea.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qmdisubwindow.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qmenu.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qmenubar.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qmouseeventtransition.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qopenglwidget.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qplaintextedit.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qtextedit.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qprogressbar.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qprogressdialog.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qproxystyle.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/QCommonStyle \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qradiobutton.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qscrollbar.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qscroller.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QPointF \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/QScrollerProperties \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qscrollerproperties.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QMetaType \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QVariant \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qshortcut.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qsizegrip.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qspinbox.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qsplashscreen.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qsplitter.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qstackedlayout.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qstackedwidget.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qstatusbar.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qstyleditemdelegate.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qstylefactory.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qstylepainter.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qstyleplugin.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qsystemtrayicon.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qtableview.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qtablewidget.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qtextbrowser.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qtoolbar.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qtoolbox.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qtoolbutton.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qtooltip.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qtreeview.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qtreewidget.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qtreewidgetitemiterator.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qundogroup.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qundostack.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qundoview.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qwhatsthis.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qwidgetaction.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qwizard.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qtwidgetsversion.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtPrintSupport/qtprintsupportglobal.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtPrintSupport/qtprintsupport-config.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtPrintSupport/qabstractprintdialog.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtPrintSupport/qpagesetupdialog.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtPrintSupport/qprintdialog.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtPrintSupport/qprintengine.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtPrintSupport/qprinter.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtPrintSupport/qprinterinfo.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QPair \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtPrintSupport/qprintpreviewdialog.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtPrintSupport/qprintpreviewwidget.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtPrintSupport/qtprintsupportversion.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtMqtt/qmqttclient.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtMqtt/qmqttglobal.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtMqtt/qmqttauthenticationproperties.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtMqtt/qmqtttype.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QHash \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtMqtt/qmqttconnectionproperties.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtMqtt/qmqttpublishproperties.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtMqtt/qmqttsubscription.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtMqtt/qmqttmessage.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtMqtt/qmqtttopicname.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QExplicitlySharedDataPointer \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtMqtt/qmqtttopicfilter.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtMqtt/qmqttsubscriptionproperties.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QIODevice \
		../../../Photoelectric_Design/QT_ShengFan/ShengFan/mqtt/qmqtt.h \
		../../../Photoelectric_Design/QT_ShengFan/ShengFan/mqtt/qmqtt_message.h \
		../../../Photoelectric_Design/QT_ShengFan/ShengFan/mqtt/qmqtt_global.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QtGlobal \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QByteArray \
		../../../Photoelectric_Design/QT_ShengFan/ShengFan/mqtt/qmqtt_client.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtNetwork/QHostAddress \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtNetwork/QAbstractSocket \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/QList \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtNetwork/QSslConfiguration \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtNetwork/qsslconfiguration.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtNetwork/qsslsocket.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtNetwork/qsslerror.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtNetwork/qsslcertificate.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtNetwork/qssl.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/QApplication
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\main.o ..\..\..\Photoelectric_Design\QT_ShengFan\ShengFan\main.cpp

debug/mainwindow.o: ../../../Photoelectric_Design/QT_ShengFan/ShengFan/mainwindow.cpp ../../../Photoelectric_Design/QT_ShengFan/ShengFan/mainwindow.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/QMainWindow \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qmainwindow.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qtwidgetsglobal.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qtguiglobal.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qglobal.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qconfig-bootstrapped.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qconfig.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qtcore-config.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qsystemdetection.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qprocessordetection.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qcompilerdetection.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qtypeinfo.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qsysinfo.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qlogging.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qflags.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qatomic.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qbasicatomic.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qatomic_bootstrap.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qgenericatomic.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qatomic_cxx11.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qatomic_msvc.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qglobalstatic.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qmutex.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qnumeric.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qversiontagging.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qtgui-config.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qtwidgets-config.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qwidget.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qwindowdefs.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qobjectdefs.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qnamespace.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qobjectdefs_impl.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qwindowdefs_win.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qobject.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qstring.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qchar.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qbytearray.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qrefcount.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qarraydata.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qstringliteral.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qstringalgorithms.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qstringview.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qstringbuilder.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qlist.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qalgorithms.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qiterator.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qhashfunctions.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qpair.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qvector.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qpoint.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qbytearraylist.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qstringlist.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qregexp.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qstringmatcher.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qcoreevent.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qscopedpointer.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qmetatype.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qvarlengtharray.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qcontainerfwd.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qobject_impl.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qmargins.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qpaintdevice.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qrect.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qsize.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qpalette.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qcolor.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qrgb.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qrgba64.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qbrush.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qmatrix.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qpolygon.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qregion.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qdatastream.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qiodevice.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qline.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qtransform.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qpainterpath.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qimage.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qpixelformat.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qpixmap.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qsharedpointer.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qshareddata.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qhash.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qsharedpointer_impl.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qfont.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qfontmetrics.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qfontinfo.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qsizepolicy.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qcursor.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qkeysequence.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qevent.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qvariant.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qmap.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qdebug.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qtextstream.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qlocale.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qset.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qcontiguouscache.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qurl.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qurlquery.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qfile.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qfiledevice.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qvector2d.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qtouchdevice.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qtabwidget.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qicon.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtNetwork/QTcpServer \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtNetwork/qtcpserver.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtNetwork/qtnetworkglobal.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtNetwork/qtnetwork-config.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtNetwork/qabstractsocket.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtNetwork/qhostaddress.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtNetwork/QTcpSocket \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtNetwork/qtcpsocket.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/QLabel \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qlabel.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qframe.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/QMessageBox \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qmessagebox.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qdialog.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QString \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QTimer \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qtimer.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qbasictimer.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/QPainter \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qpainter.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qtextoption.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qpen.h \
		../../../Photoelectric_Design/QT_ShengFan/ShengFan/qcustomplot.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QObject \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QPointer \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qpointer.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QSharedPointer \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/QPaintEvent \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/QMouseEvent \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/QWheelEvent \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/QPixmap \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QVector \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QDateTime \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qdatetime.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QMultiMap \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QFlags \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QDebug \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QStack \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qstack.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QCache \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qcache.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QMargins \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qmath.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/QOpenGLContext \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qopenglcontext.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QScopedPointer \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/QSurfaceFormat \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qsurfaceformat.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qopengl.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qt_windows.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtANGLE/GLES3/gl3.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtANGLE/GLES3/gl3platform.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtANGLE/KHR/khrplatform.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtANGLE/GLES2/gl2.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtANGLE/GLES2/gl2platform.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qopengles2ext.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qopenglext.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qopenglversionfunctions.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/QOpenGLFramebufferObject \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qopenglframebufferobject.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/QOffscreenSurface \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qoffscreensurface.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qsurface.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/QWindow \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qwindow.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QEvent \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QRect \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtOpenGL/QGLPixelBuffer \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtOpenGL/qglpixelbuffer.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtOpenGL/qgl.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qpaintengine.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtOpenGL/qglcolormap.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtOpenGL/qtopenglglobal.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QtNumeric \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/QWidget \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtPrintSupport/QtPrintSupport \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtPrintSupport/QtPrintSupportDepends \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QtCore \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QtCoreDepends \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qabstractanimation.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qabstracteventdispatcher.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qeventloop.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qabstractitemmodel.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qabstractnativeeventfilter.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qabstractproxymodel.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qabstractstate.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qabstracttransition.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qanimationgroup.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qarraydataops.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qarraydatapointer.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qbitarray.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qbuffer.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qbytearraymatcher.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qcborarray.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qcborvalue.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qcborcommon.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qregularexpression.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/quuid.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qcbormap.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qcborstream.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qfloat16.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qcollator.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qcommandlineoption.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qcommandlineparser.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qcoreapplication.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qconcatenatetablesproxymodel.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qcryptographichash.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qdeadlinetimer.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qelapsedtimer.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qdir.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qfileinfo.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qdiriterator.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qeasingcurve.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qendian.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qeventtransition.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qexception.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qfactoryinterface.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qfileselector.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QStringList \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qfilesystemwatcher.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qfinalstate.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qfuture.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qfutureinterface.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qrunnable.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qresultstore.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qfuturesynchronizer.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qfuturewatcher.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qhistorystate.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qidentityproxymodel.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qisenum.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qitemselectionmodel.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qjsonarray.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qjsonvalue.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qjsondocument.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qjsonobject.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qlibrary.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qlibraryinfo.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qversionnumber.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qlinkedlist.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qlockfile.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qloggingcategory.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qmessageauthenticationcode.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qmetaobject.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qmimedata.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qmimedatabase.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qmimetype.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qobjectcleanuphandler.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qoperatingsystemversion.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qparallelanimationgroup.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qpauseanimation.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qplugin.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qpluginloader.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qprocess.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qpropertyanimation.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qvariantanimation.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qqueue.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qrandom.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qreadwritelock.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qresource.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qsavefile.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qscopedvaluerollback.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qscopeguard.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qsemaphore.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qsequentialanimationgroup.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qsettings.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qsharedmemory.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qsignalmapper.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qsignaltransition.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qsocketnotifier.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qsortfilterproxymodel.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qstandardpaths.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qstate.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qstatemachine.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qstorageinfo.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qstringlistmodel.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qsystemsemaphore.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qtemporarydir.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qtemporaryfile.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qtextboundaryfinder.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qtextcodec.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qthread.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qthreadpool.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qthreadstorage.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qtimeline.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qtimezone.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qtranslator.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qtransposeproxymodel.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qtypetraits.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qwaitcondition.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qwineventnotifier.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qxmlstream.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qtcoreversion.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/QtGui \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/QtGuiDepends \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qabstracttextdocumentlayout.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qtextlayout.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qtextformat.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qglyphrun.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qrawfont.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qfontdatabase.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qtextcursor.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qtextdocument.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qaccessible.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qaccessiblebridge.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qaccessibleobject.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qaccessibleplugin.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qbackingstore.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qbitmap.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qclipboard.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qdesktopservices.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qdrag.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qgenericmatrix.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qgenericplugin.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qgenericpluginfactory.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qguiapplication.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qinputmethod.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qiconengine.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qiconengineplugin.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qimageiohandler.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qimagereader.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qimagewriter.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qmatrix4x4.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qvector3d.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qvector4d.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qquaternion.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qmovie.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qopenglbuffer.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qopengldebug.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qopenglextrafunctions.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qopenglfunctions.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qopenglpaintdevice.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qopenglpixeltransferoptions.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QSharedDataPointer \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qopenglshaderprogram.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qopengltexture.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qopengltextureblitter.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/QMatrix3x3 \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/QMatrix4x4 \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qopengltimerquery.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qopenglvertexarrayobject.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qopenglwindow.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/QPaintDeviceWindow \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qpaintdevicewindow.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/QPaintDevice \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/QImage \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qpagedpaintdevice.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qpagelayout.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qpagesize.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qpdfwriter.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qpicture.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qpictureformatplugin.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qpixmapcache.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qrasterwindow.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qscreen.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QList \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QSize \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QSizeF \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/QTransform \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qsessionmanager.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qstandarditemmodel.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qstatictext.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qstylehints.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qsyntaxhighlighter.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qtextobject.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qtextdocumentfragment.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qtextdocumentwriter.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qtextlist.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qtexttable.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qvalidator.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qtguiversion.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/QtWidgets \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/QtWidgetsDepends \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qabstractbutton.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qabstractitemdelegate.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qstyleoption.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qabstractspinbox.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qslider.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qabstractslider.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qstyle.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qtabbar.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qrubberband.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qabstractitemview.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qabstractscrollarea.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qaccessiblewidget.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qaction.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qactiongroup.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qapplication.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qdesktopwidget.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qboxlayout.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qlayout.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qlayoutitem.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qgridlayout.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qbuttongroup.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qcalendarwidget.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qcheckbox.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qcolordialog.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qcolormap.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qcolumnview.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qcombobox.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qcommandlinkbutton.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qpushbutton.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qcommonstyle.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qcompleter.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qdatawidgetmapper.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qdatetimeedit.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qdial.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qdialogbuttonbox.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qdirmodel.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qfileiconprovider.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qdockwidget.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qdrawutil.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qerrormessage.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qfiledialog.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qfilesystemmodel.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qfocusframe.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qfontcombobox.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qfontdialog.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qformlayout.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/QLayout \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qgesture.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qgesturerecognizer.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qgraphicsanchorlayout.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qgraphicsitem.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qgraphicslayout.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qgraphicslayoutitem.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qgraphicseffect.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qgraphicsgridlayout.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qgraphicsitemanimation.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qgraphicslinearlayout.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qgraphicsproxywidget.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qgraphicswidget.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qgraphicsscene.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qgraphicssceneevent.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qgraphicstransform.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/QVector3D \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qgraphicsview.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qscrollarea.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qgroupbox.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qheaderview.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qinputdialog.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qlineedit.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qitemdelegate.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qitemeditorfactory.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qkeyeventtransition.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qkeysequenceedit.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qlcdnumber.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qlistview.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qlistwidget.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qmdiarea.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qmdisubwindow.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qmenu.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qmenubar.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qmouseeventtransition.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qopenglwidget.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qplaintextedit.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qtextedit.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qprogressbar.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qprogressdialog.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qproxystyle.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/QCommonStyle \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qradiobutton.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qscrollbar.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qscroller.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QPointF \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/QScrollerProperties \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qscrollerproperties.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QMetaType \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QVariant \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qshortcut.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qsizegrip.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qspinbox.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qsplashscreen.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qsplitter.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qstackedlayout.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qstackedwidget.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qstatusbar.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qstyleditemdelegate.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qstylefactory.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qstylepainter.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qstyleplugin.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qsystemtrayicon.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qtableview.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qtablewidget.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qtextbrowser.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qtoolbar.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qtoolbox.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qtoolbutton.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qtooltip.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qtreeview.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qtreewidget.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qtreewidgetitemiterator.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qundogroup.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qundostack.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qundoview.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qwhatsthis.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qwidgetaction.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qwizard.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qtwidgetsversion.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtPrintSupport/qtprintsupportglobal.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtPrintSupport/qtprintsupport-config.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtPrintSupport/qabstractprintdialog.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtPrintSupport/qpagesetupdialog.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtPrintSupport/qprintdialog.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtPrintSupport/qprintengine.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtPrintSupport/qprinter.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtPrintSupport/qprinterinfo.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QPair \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtPrintSupport/qprintpreviewdialog.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtPrintSupport/qprintpreviewwidget.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtPrintSupport/qtprintsupportversion.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtMqtt/qmqttclient.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtMqtt/qmqttglobal.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtMqtt/qmqttauthenticationproperties.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtMqtt/qmqtttype.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QHash \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtMqtt/qmqttconnectionproperties.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtMqtt/qmqttpublishproperties.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtMqtt/qmqttsubscription.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtMqtt/qmqttmessage.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtMqtt/qmqtttopicname.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QExplicitlySharedDataPointer \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtMqtt/qmqtttopicfilter.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtMqtt/qmqttsubscriptionproperties.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QIODevice \
		../../../Photoelectric_Design/QT_ShengFan/ShengFan/mqtt/qmqtt.h \
		../../../Photoelectric_Design/QT_ShengFan/ShengFan/mqtt/qmqtt_message.h \
		../../../Photoelectric_Design/QT_ShengFan/ShengFan/mqtt/qmqtt_global.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QtGlobal \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QByteArray \
		../../../Photoelectric_Design/QT_ShengFan/ShengFan/mqtt/qmqtt_client.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtNetwork/QHostAddress \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtNetwork/QAbstractSocket \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/QList \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtNetwork/QSslConfiguration \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtNetwork/qsslconfiguration.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtNetwork/qsslsocket.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtNetwork/qsslerror.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtNetwork/qsslcertificate.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtNetwork/qssl.h \
		ui_mainwindow.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/QIcon \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/QApplication \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/QCheckBox \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/QComboBox \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/QFrame \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/QHBoxLayout \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/QMenuBar \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/QSpacerItem \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/QStatusBar \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/QVBoxLayout
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\mainwindow.o ..\..\..\Photoelectric_Design\QT_ShengFan\ShengFan\mainwindow.cpp

debug/qcustomplot.o: ../../../Photoelectric_Design/QT_ShengFan/ShengFan/qcustomplot.cpp ../../../Photoelectric_Design/QT_ShengFan/ShengFan/qcustomplot.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qglobal.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qconfig-bootstrapped.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qconfig.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qtcore-config.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qsystemdetection.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qprocessordetection.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qcompilerdetection.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qtypeinfo.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qsysinfo.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qlogging.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qflags.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qatomic.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qbasicatomic.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qatomic_bootstrap.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qgenericatomic.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qatomic_cxx11.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qatomic_msvc.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qglobalstatic.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qmutex.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qnumeric.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qversiontagging.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QObject \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qobject.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qobjectdefs.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qnamespace.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qobjectdefs_impl.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qstring.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qchar.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qbytearray.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qrefcount.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qarraydata.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qstringliteral.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qstringalgorithms.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qstringview.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qstringbuilder.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qlist.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qalgorithms.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qiterator.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qhashfunctions.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qpair.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qvector.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qpoint.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qbytearraylist.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qstringlist.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qregexp.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qstringmatcher.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qcoreevent.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qscopedpointer.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qmetatype.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qvarlengtharray.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qcontainerfwd.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qobject_impl.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QPointer \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qpointer.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qsharedpointer.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qshareddata.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qhash.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qsharedpointer_impl.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QSharedPointer \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QTimer \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qtimer.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qbasictimer.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/QPainter \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qpainter.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qtguiglobal.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qtgui-config.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qrect.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qmargins.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qsize.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qpixmap.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qpaintdevice.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qwindowdefs.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qwindowdefs_win.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qcolor.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qrgb.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qrgba64.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qimage.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qpixelformat.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qtransform.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qmatrix.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qpolygon.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qregion.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qdatastream.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qiodevice.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qline.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qpainterpath.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qtextoption.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qpen.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qbrush.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qfontinfo.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qfont.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qfontmetrics.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/QPaintEvent \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qevent.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qkeysequence.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qvariant.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qmap.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qdebug.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qtextstream.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qlocale.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qset.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qcontiguouscache.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qurl.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qurlquery.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qfile.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qfiledevice.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qvector2d.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qtouchdevice.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/QMouseEvent \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/QWheelEvent \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/QPixmap \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QVector \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QString \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QDateTime \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qdatetime.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QMultiMap \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QFlags \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QDebug \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QStack \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qstack.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QCache \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qcache.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QMargins \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qmath.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/QOpenGLContext \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qopenglcontext.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QScopedPointer \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/QSurfaceFormat \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qsurfaceformat.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qopengl.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qt_windows.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtANGLE/GLES3/gl3.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtANGLE/GLES3/gl3platform.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtANGLE/KHR/khrplatform.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtANGLE/GLES2/gl2.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtANGLE/GLES2/gl2platform.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qopengles2ext.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qopenglext.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qopenglversionfunctions.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/QOpenGLFramebufferObject \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qopenglframebufferobject.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/QOffscreenSurface \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qoffscreensurface.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qsurface.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/QWindow \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qwindow.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QEvent \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QRect \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qicon.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qcursor.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtOpenGL/QGLPixelBuffer \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtOpenGL/qglpixelbuffer.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtOpenGL/qgl.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qwidget.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qtwidgetsglobal.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qtwidgets-config.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qpalette.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qsizepolicy.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qpaintengine.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtOpenGL/qglcolormap.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtOpenGL/qtopenglglobal.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QtNumeric \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/QWidget \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtPrintSupport/QtPrintSupport \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtPrintSupport/QtPrintSupportDepends \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QtCore \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QtCoreDepends \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qabstractanimation.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qabstracteventdispatcher.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qeventloop.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qabstractitemmodel.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qabstractnativeeventfilter.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qabstractproxymodel.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qabstractstate.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qabstracttransition.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qanimationgroup.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qarraydataops.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qarraydatapointer.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qbitarray.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qbuffer.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qbytearraymatcher.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qcborarray.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qcborvalue.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qcborcommon.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qregularexpression.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/quuid.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qcbormap.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qcborstream.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qfloat16.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qcollator.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qcommandlineoption.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qcommandlineparser.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qcoreapplication.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qconcatenatetablesproxymodel.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qcryptographichash.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qdeadlinetimer.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qelapsedtimer.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qdir.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qfileinfo.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qdiriterator.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qeasingcurve.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qendian.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qeventtransition.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qexception.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qfactoryinterface.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qfileselector.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QStringList \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qfilesystemwatcher.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qfinalstate.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qfuture.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qfutureinterface.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qrunnable.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qresultstore.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qfuturesynchronizer.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qfuturewatcher.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qhistorystate.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qidentityproxymodel.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qisenum.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qitemselectionmodel.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qjsonarray.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qjsonvalue.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qjsondocument.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qjsonobject.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qlibrary.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qlibraryinfo.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qversionnumber.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qlinkedlist.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qlockfile.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qloggingcategory.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qmessageauthenticationcode.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qmetaobject.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qmimedata.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qmimedatabase.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qmimetype.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qobjectcleanuphandler.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qoperatingsystemversion.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qparallelanimationgroup.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qpauseanimation.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qplugin.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qpluginloader.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qprocess.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qpropertyanimation.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qvariantanimation.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qqueue.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qrandom.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qreadwritelock.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qresource.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qsavefile.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qscopedvaluerollback.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qscopeguard.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qsemaphore.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qsequentialanimationgroup.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qsettings.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qsharedmemory.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qsignalmapper.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qsignaltransition.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qsocketnotifier.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qsortfilterproxymodel.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qstandardpaths.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qstate.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qstatemachine.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qstorageinfo.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qstringlistmodel.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qsystemsemaphore.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qtemporarydir.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qtemporaryfile.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qtextboundaryfinder.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qtextcodec.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qthread.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qthreadpool.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qthreadstorage.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qtimeline.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qtimezone.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qtranslator.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qtransposeproxymodel.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qtypetraits.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qwaitcondition.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qwineventnotifier.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qxmlstream.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/qtcoreversion.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/QtGui \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/QtGuiDepends \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qabstracttextdocumentlayout.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qtextlayout.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qtextformat.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qglyphrun.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qrawfont.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qfontdatabase.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qtextcursor.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qtextdocument.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qaccessible.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qaccessiblebridge.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qaccessibleobject.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qaccessibleplugin.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qbackingstore.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qbitmap.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qclipboard.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qdesktopservices.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qdrag.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qgenericmatrix.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qgenericplugin.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qgenericpluginfactory.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qguiapplication.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qinputmethod.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qiconengine.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qiconengineplugin.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qimageiohandler.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qimagereader.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qimagewriter.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qmatrix4x4.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qvector3d.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qvector4d.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qquaternion.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qmovie.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qopenglbuffer.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qopengldebug.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qopenglextrafunctions.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qopenglfunctions.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qopenglpaintdevice.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qopenglpixeltransferoptions.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QSharedDataPointer \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qopenglshaderprogram.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qopengltexture.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qopengltextureblitter.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/QMatrix3x3 \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/QMatrix4x4 \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qopengltimerquery.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qopenglvertexarrayobject.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qopenglwindow.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/QPaintDeviceWindow \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qpaintdevicewindow.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/QPaintDevice \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/QImage \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qpagedpaintdevice.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qpagelayout.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qpagesize.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qpdfwriter.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qpicture.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qpictureformatplugin.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qpixmapcache.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qrasterwindow.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qscreen.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QList \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QSize \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QSizeF \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/QTransform \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qsessionmanager.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qstandarditemmodel.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qstatictext.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qstylehints.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qsyntaxhighlighter.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qtextobject.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qtextdocumentfragment.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qtextdocumentwriter.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qtextlist.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qtexttable.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qvalidator.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/qtguiversion.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/QtWidgets \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/QtWidgetsDepends \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qabstractbutton.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qabstractitemdelegate.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qstyleoption.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qabstractspinbox.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qslider.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qabstractslider.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qstyle.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qtabbar.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qtabwidget.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qrubberband.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qframe.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qabstractitemview.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qabstractscrollarea.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qaccessiblewidget.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qaction.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qactiongroup.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qapplication.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qdesktopwidget.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qboxlayout.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qlayout.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qlayoutitem.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qgridlayout.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qbuttongroup.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qcalendarwidget.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qcheckbox.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qcolordialog.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qdialog.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qcolormap.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qcolumnview.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qcombobox.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qcommandlinkbutton.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qpushbutton.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qcommonstyle.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qcompleter.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qdatawidgetmapper.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qdatetimeedit.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qdial.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qdialogbuttonbox.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qdirmodel.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qfileiconprovider.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qdockwidget.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qdrawutil.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qerrormessage.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qfiledialog.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qfilesystemmodel.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qfocusframe.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qfontcombobox.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qfontdialog.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qformlayout.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/QLayout \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qgesture.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qgesturerecognizer.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qgraphicsanchorlayout.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qgraphicsitem.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qgraphicslayout.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qgraphicslayoutitem.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qgraphicseffect.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qgraphicsgridlayout.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qgraphicsitemanimation.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qgraphicslinearlayout.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qgraphicsproxywidget.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qgraphicswidget.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qgraphicsscene.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qgraphicssceneevent.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qgraphicstransform.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtGui/QVector3D \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qgraphicsview.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qscrollarea.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qgroupbox.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qheaderview.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qinputdialog.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qlineedit.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qitemdelegate.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qitemeditorfactory.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qkeyeventtransition.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qkeysequenceedit.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qlabel.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qlcdnumber.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qlistview.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qlistwidget.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qmainwindow.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qmdiarea.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qmdisubwindow.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qmenu.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qmenubar.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qmessagebox.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qmouseeventtransition.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qopenglwidget.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qplaintextedit.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qtextedit.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qprogressbar.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qprogressdialog.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qproxystyle.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/QCommonStyle \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qradiobutton.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qscrollbar.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qscroller.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QPointF \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/QScrollerProperties \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qscrollerproperties.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QMetaType \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QVariant \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qshortcut.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qsizegrip.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qspinbox.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qsplashscreen.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qsplitter.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qstackedlayout.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qstackedwidget.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qstatusbar.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qstyleditemdelegate.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qstylefactory.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qstylepainter.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qstyleplugin.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qsystemtrayicon.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qtableview.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qtablewidget.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qtextbrowser.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qtoolbar.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qtoolbox.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qtoolbutton.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qtooltip.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qtreeview.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qtreewidget.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qtreewidgetitemiterator.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qundogroup.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qundostack.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qundoview.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qwhatsthis.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qwidgetaction.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qwizard.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtWidgets/qtwidgetsversion.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtPrintSupport/qtprintsupportglobal.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtPrintSupport/qtprintsupport-config.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtPrintSupport/qabstractprintdialog.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtPrintSupport/qpagesetupdialog.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtPrintSupport/qprintdialog.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtPrintSupport/qprintengine.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtPrintSupport/qprinter.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtPrintSupport/qprinterinfo.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtCore/QPair \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtPrintSupport/qprintpreviewdialog.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtPrintSupport/qprintpreviewwidget.h \
		D:/Software/QT5.13.2/5.13.2/mingw73_32/include/QtPrintSupport/qtprintsupportversion.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\qcustomplot.o ..\..\..\Photoelectric_Design\QT_ShengFan\ShengFan\qcustomplot.cpp

debug/qrc_qrc.o: debug/qrc_qrc.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\qrc_qrc.o debug\qrc_qrc.cpp

debug/moc_mainwindow.o: debug/moc_mainwindow.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\moc_mainwindow.o debug\moc_mainwindow.cpp

debug/moc_qcustomplot.o: debug/moc_qcustomplot.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\moc_qcustomplot.o debug\moc_qcustomplot.cpp

####### Install

install:  FORCE

uninstall:  FORCE

FORCE:

