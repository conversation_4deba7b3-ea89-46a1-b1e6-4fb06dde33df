/****************************************************************************
** Meta object code from reading C++ file 'qcustomplot.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.13.2)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include <memory>
#include "../../ShengFan/qcustomplot.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#include <QtCore/QSharedPointer>
#include <QtCore/QList>
#include <QtCore/QVector>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'qcustomplot.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.13.2. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_QCP_t {
    QByteArrayData data[62];
    char stringdata0[713];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_QCP_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_QCP_t qt_meta_stringdata_QCP = {
    {
QT_MOC_LITERAL(0, 0, 3), // "QCP"
QT_MOC_LITERAL(1, 4, 14), // "ResolutionUnit"
QT_MOC_LITERAL(2, 19, 14), // "ruDotsPerMeter"
QT_MOC_LITERAL(3, 34, 19), // "ruDotsPerCentimeter"
QT_MOC_LITERAL(4, 54, 13), // "ruDotsPerInch"
QT_MOC_LITERAL(5, 68, 9), // "ExportPen"
QT_MOC_LITERAL(6, 78, 12), // "epNoCosmetic"
QT_MOC_LITERAL(7, 91, 15), // "epAllowCosmetic"
QT_MOC_LITERAL(8, 107, 10), // "SignDomain"
QT_MOC_LITERAL(9, 118, 10), // "sdNegative"
QT_MOC_LITERAL(10, 129, 6), // "sdBoth"
QT_MOC_LITERAL(11, 136, 10), // "sdPositive"
QT_MOC_LITERAL(12, 147, 10), // "MarginSide"
QT_MOC_LITERAL(13, 158, 6), // "msLeft"
QT_MOC_LITERAL(14, 165, 7), // "msRight"
QT_MOC_LITERAL(15, 173, 5), // "msTop"
QT_MOC_LITERAL(16, 179, 8), // "msBottom"
QT_MOC_LITERAL(17, 188, 5), // "msAll"
QT_MOC_LITERAL(18, 194, 6), // "msNone"
QT_MOC_LITERAL(19, 201, 11), // "MarginSides"
QT_MOC_LITERAL(20, 213, 18), // "AntialiasedElement"
QT_MOC_LITERAL(21, 232, 6), // "aeAxes"
QT_MOC_LITERAL(22, 239, 6), // "aeGrid"
QT_MOC_LITERAL(23, 246, 9), // "aeSubGrid"
QT_MOC_LITERAL(24, 256, 8), // "aeLegend"
QT_MOC_LITERAL(25, 265, 13), // "aeLegendItems"
QT_MOC_LITERAL(26, 279, 12), // "aePlottables"
QT_MOC_LITERAL(27, 292, 7), // "aeItems"
QT_MOC_LITERAL(28, 300, 10), // "aeScatters"
QT_MOC_LITERAL(29, 311, 7), // "aeFills"
QT_MOC_LITERAL(30, 319, 10), // "aeZeroLine"
QT_MOC_LITERAL(31, 330, 7), // "aeOther"
QT_MOC_LITERAL(32, 338, 5), // "aeAll"
QT_MOC_LITERAL(33, 344, 6), // "aeNone"
QT_MOC_LITERAL(34, 351, 19), // "AntialiasedElements"
QT_MOC_LITERAL(35, 371, 12), // "PlottingHint"
QT_MOC_LITERAL(36, 384, 6), // "phNone"
QT_MOC_LITERAL(37, 391, 15), // "phFastPolylines"
QT_MOC_LITERAL(38, 407, 18), // "phImmediateRefresh"
QT_MOC_LITERAL(39, 426, 13), // "phCacheLabels"
QT_MOC_LITERAL(40, 440, 13), // "PlottingHints"
QT_MOC_LITERAL(41, 454, 11), // "Interaction"
QT_MOC_LITERAL(42, 466, 10), // "iRangeDrag"
QT_MOC_LITERAL(43, 477, 10), // "iRangeZoom"
QT_MOC_LITERAL(44, 488, 12), // "iMultiSelect"
QT_MOC_LITERAL(45, 501, 17), // "iSelectPlottables"
QT_MOC_LITERAL(46, 519, 11), // "iSelectAxes"
QT_MOC_LITERAL(47, 531, 13), // "iSelectLegend"
QT_MOC_LITERAL(48, 545, 12), // "iSelectItems"
QT_MOC_LITERAL(49, 558, 12), // "iSelectOther"
QT_MOC_LITERAL(50, 571, 12), // "Interactions"
QT_MOC_LITERAL(51, 584, 17), // "SelectionRectMode"
QT_MOC_LITERAL(52, 602, 7), // "srmNone"
QT_MOC_LITERAL(53, 610, 7), // "srmZoom"
QT_MOC_LITERAL(54, 618, 9), // "srmSelect"
QT_MOC_LITERAL(55, 628, 9), // "srmCustom"
QT_MOC_LITERAL(56, 638, 13), // "SelectionType"
QT_MOC_LITERAL(57, 652, 6), // "stNone"
QT_MOC_LITERAL(58, 659, 7), // "stWhole"
QT_MOC_LITERAL(59, 667, 12), // "stSingleData"
QT_MOC_LITERAL(60, 680, 11), // "stDataRange"
QT_MOC_LITERAL(61, 692, 20) // "stMultipleDataRanges"

    },
    "QCP\0ResolutionUnit\0ruDotsPerMeter\0"
    "ruDotsPerCentimeter\0ruDotsPerInch\0"
    "ExportPen\0epNoCosmetic\0epAllowCosmetic\0"
    "SignDomain\0sdNegative\0sdBoth\0sdPositive\0"
    "MarginSide\0msLeft\0msRight\0msTop\0"
    "msBottom\0msAll\0msNone\0MarginSides\0"
    "AntialiasedElement\0aeAxes\0aeGrid\0"
    "aeSubGrid\0aeLegend\0aeLegendItems\0"
    "aePlottables\0aeItems\0aeScatters\0aeFills\0"
    "aeZeroLine\0aeOther\0aeAll\0aeNone\0"
    "AntialiasedElements\0PlottingHint\0"
    "phNone\0phFastPolylines\0phImmediateRefresh\0"
    "phCacheLabels\0PlottingHints\0Interaction\0"
    "iRangeDrag\0iRangeZoom\0iMultiSelect\0"
    "iSelectPlottables\0iSelectAxes\0"
    "iSelectLegend\0iSelectItems\0iSelectOther\0"
    "Interactions\0SelectionRectMode\0srmNone\0"
    "srmZoom\0srmSelect\0srmCustom\0SelectionType\0"
    "stNone\0stWhole\0stSingleData\0stDataRange\0"
    "stMultipleDataRanges"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_QCP[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
       0,    0, // methods
       0,    0, // properties
      13,   14, // enums/sets
       0,    0, // constructors
       4,       // flags
       0,       // signalCount

 // enums: name, alias, flags, count, data
       1,    1, 0x0,    3,   79,
       5,    5, 0x0,    2,   85,
       8,    8, 0x0,    3,   89,
      12,   12, 0x0,    6,   95,
      19,   12, 0x1,    6,  107,
      20,   20, 0x0,   13,  119,
      34,   20, 0x1,   13,  145,
      35,   35, 0x0,    4,  171,
      40,   35, 0x1,    4,  179,
      41,   41, 0x0,    8,  187,
      50,   41, 0x1,    8,  203,
      51,   51, 0x0,    4,  219,
      56,   56, 0x0,    5,  227,

 // enum data: key, value
       2, uint(QCP::ruDotsPerMeter),
       3, uint(QCP::ruDotsPerCentimeter),
       4, uint(QCP::ruDotsPerInch),
       6, uint(QCP::epNoCosmetic),
       7, uint(QCP::epAllowCosmetic),
       9, uint(QCP::sdNegative),
      10, uint(QCP::sdBoth),
      11, uint(QCP::sdPositive),
      13, uint(QCP::msLeft),
      14, uint(QCP::msRight),
      15, uint(QCP::msTop),
      16, uint(QCP::msBottom),
      17, uint(QCP::msAll),
      18, uint(QCP::msNone),
      13, uint(QCP::msLeft),
      14, uint(QCP::msRight),
      15, uint(QCP::msTop),
      16, uint(QCP::msBottom),
      17, uint(QCP::msAll),
      18, uint(QCP::msNone),
      21, uint(QCP::aeAxes),
      22, uint(QCP::aeGrid),
      23, uint(QCP::aeSubGrid),
      24, uint(QCP::aeLegend),
      25, uint(QCP::aeLegendItems),
      26, uint(QCP::aePlottables),
      27, uint(QCP::aeItems),
      28, uint(QCP::aeScatters),
      29, uint(QCP::aeFills),
      30, uint(QCP::aeZeroLine),
      31, uint(QCP::aeOther),
      32, uint(QCP::aeAll),
      33, uint(QCP::aeNone),
      21, uint(QCP::aeAxes),
      22, uint(QCP::aeGrid),
      23, uint(QCP::aeSubGrid),
      24, uint(QCP::aeLegend),
      25, uint(QCP::aeLegendItems),
      26, uint(QCP::aePlottables),
      27, uint(QCP::aeItems),
      28, uint(QCP::aeScatters),
      29, uint(QCP::aeFills),
      30, uint(QCP::aeZeroLine),
      31, uint(QCP::aeOther),
      32, uint(QCP::aeAll),
      33, uint(QCP::aeNone),
      36, uint(QCP::phNone),
      37, uint(QCP::phFastPolylines),
      38, uint(QCP::phImmediateRefresh),
      39, uint(QCP::phCacheLabels),
      36, uint(QCP::phNone),
      37, uint(QCP::phFastPolylines),
      38, uint(QCP::phImmediateRefresh),
      39, uint(QCP::phCacheLabels),
      42, uint(QCP::iRangeDrag),
      43, uint(QCP::iRangeZoom),
      44, uint(QCP::iMultiSelect),
      45, uint(QCP::iSelectPlottables),
      46, uint(QCP::iSelectAxes),
      47, uint(QCP::iSelectLegend),
      48, uint(QCP::iSelectItems),
      49, uint(QCP::iSelectOther),
      42, uint(QCP::iRangeDrag),
      43, uint(QCP::iRangeZoom),
      44, uint(QCP::iMultiSelect),
      45, uint(QCP::iSelectPlottables),
      46, uint(QCP::iSelectAxes),
      47, uint(QCP::iSelectLegend),
      48, uint(QCP::iSelectItems),
      49, uint(QCP::iSelectOther),
      52, uint(QCP::srmNone),
      53, uint(QCP::srmZoom),
      54, uint(QCP::srmSelect),
      55, uint(QCP::srmCustom),
      57, uint(QCP::stNone),
      58, uint(QCP::stWhole),
      59, uint(QCP::stSingleData),
      60, uint(QCP::stDataRange),
      61, uint(QCP::stMultipleDataRanges),

       0        // eod
};

QT_INIT_METAOBJECT const QMetaObject QCP::staticMetaObject = { {
    nullptr,
    qt_meta_stringdata_QCP.data,
    qt_meta_data_QCP,
    nullptr,
    nullptr,
    nullptr
} };

struct qt_meta_stringdata_QCPPainter_t {
    QByteArrayData data[7];
    char stringdata0[85];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_QCPPainter_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_QCPPainter_t qt_meta_stringdata_QCPPainter = {
    {
QT_MOC_LITERAL(0, 0, 10), // "QCPPainter"
QT_MOC_LITERAL(1, 11, 11), // "PainterMode"
QT_MOC_LITERAL(2, 23, 9), // "pmDefault"
QT_MOC_LITERAL(3, 33, 12), // "pmVectorized"
QT_MOC_LITERAL(4, 46, 11), // "pmNoCaching"
QT_MOC_LITERAL(5, 58, 13), // "pmNonCosmetic"
QT_MOC_LITERAL(6, 72, 12) // "PainterModes"

    },
    "QCPPainter\0PainterMode\0pmDefault\0"
    "pmVectorized\0pmNoCaching\0pmNonCosmetic\0"
    "PainterModes"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_QCPPainter[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
       0,    0, // methods
       0,    0, // properties
       2,   14, // enums/sets
       0,    0, // constructors
       4,       // flags
       0,       // signalCount

 // enums: name, alias, flags, count, data
       1,    1, 0x0,    4,   24,
       6,    1, 0x1,    4,   32,

 // enum data: key, value
       2, uint(QCPPainter::pmDefault),
       3, uint(QCPPainter::pmVectorized),
       4, uint(QCPPainter::pmNoCaching),
       5, uint(QCPPainter::pmNonCosmetic),
       2, uint(QCPPainter::pmDefault),
       3, uint(QCPPainter::pmVectorized),
       4, uint(QCPPainter::pmNoCaching),
       5, uint(QCPPainter::pmNonCosmetic),

       0        // eod
};

QT_INIT_METAOBJECT const QMetaObject QCPPainter::staticMetaObject = { {
    &QPainter::staticMetaObject,
    qt_meta_stringdata_QCPPainter.data,
    qt_meta_data_QCPPainter,
    nullptr,
    nullptr,
    nullptr
} };

struct qt_meta_stringdata_QCPLayer_t {
    QByteArrayData data[12];
    char stringdata0[118];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_QCPLayer_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_QCPLayer_t qt_meta_stringdata_QCPLayer = {
    {
QT_MOC_LITERAL(0, 0, 8), // "QCPLayer"
QT_MOC_LITERAL(1, 9, 10), // "parentPlot"
QT_MOC_LITERAL(2, 20, 12), // "QCustomPlot*"
QT_MOC_LITERAL(3, 33, 4), // "name"
QT_MOC_LITERAL(4, 38, 5), // "index"
QT_MOC_LITERAL(5, 44, 8), // "children"
QT_MOC_LITERAL(6, 53, 20), // "QList<QCPLayerable*>"
QT_MOC_LITERAL(7, 74, 7), // "visible"
QT_MOC_LITERAL(8, 82, 4), // "mode"
QT_MOC_LITERAL(9, 87, 9), // "LayerMode"
QT_MOC_LITERAL(10, 97, 9), // "lmLogical"
QT_MOC_LITERAL(11, 107, 10) // "lmBuffered"

    },
    "QCPLayer\0parentPlot\0QCustomPlot*\0name\0"
    "index\0children\0QList<QCPLayerable*>\0"
    "visible\0mode\0LayerMode\0lmLogical\0"
    "lmBuffered"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_QCPLayer[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
       0,    0, // methods
       6,   14, // properties
       1,   32, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

 // properties: name, type, flags
       1, 0x80000000 | 2, 0x00095009,
       3, QMetaType::QString, 0x00095001,
       4, QMetaType::Int, 0x00095001,
       5, 0x80000000 | 6, 0x00095009,
       7, QMetaType::Bool, 0x00095103,
       8, 0x80000000 | 9, 0x0009510b,

 // enums: name, alias, flags, count, data
       9,    9, 0x0,    2,   37,

 // enum data: key, value
      10, uint(QCPLayer::lmLogical),
      11, uint(QCPLayer::lmBuffered),

       0        // eod
};

void QCPLayer::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::RegisterPropertyMetaType) {
        switch (_id) {
        default: *reinterpret_cast<int*>(_a[0]) = -1; break;
        case 0:
            *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< QCustomPlot* >(); break;
        case 3:
            *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< QList<QCPLayerable*> >(); break;
        }
    }

#ifndef QT_NO_PROPERTIES
    else if (_c == QMetaObject::ReadProperty) {
        auto *_t = static_cast<QCPLayer *>(_o);
        Q_UNUSED(_t)
        void *_v = _a[0];
        switch (_id) {
        case 0: *reinterpret_cast< QCustomPlot**>(_v) = _t->parentPlot(); break;
        case 1: *reinterpret_cast< QString*>(_v) = _t->name(); break;
        case 2: *reinterpret_cast< int*>(_v) = _t->index(); break;
        case 3: *reinterpret_cast< QList<QCPLayerable*>*>(_v) = _t->children(); break;
        case 4: *reinterpret_cast< bool*>(_v) = _t->visible(); break;
        case 5: *reinterpret_cast< LayerMode*>(_v) = _t->mode(); break;
        default: break;
        }
    } else if (_c == QMetaObject::WriteProperty) {
        auto *_t = static_cast<QCPLayer *>(_o);
        Q_UNUSED(_t)
        void *_v = _a[0];
        switch (_id) {
        case 4: _t->setVisible(*reinterpret_cast< bool*>(_v)); break;
        case 5: _t->setMode(*reinterpret_cast< LayerMode*>(_v)); break;
        default: break;
        }
    } else if (_c == QMetaObject::ResetProperty) {
    }
#endif // QT_NO_PROPERTIES
    Q_UNUSED(_o);
}

QT_INIT_METAOBJECT const QMetaObject QCPLayer::staticMetaObject = { {
    &QObject::staticMetaObject,
    qt_meta_stringdata_QCPLayer.data,
    qt_meta_data_QCPLayer,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *QCPLayer::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *QCPLayer::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_QCPLayer.stringdata0))
        return static_cast<void*>(this);
    return QObject::qt_metacast(_clname);
}

int QCPLayer::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QObject::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    
#ifndef QT_NO_PROPERTIES
    if (_c == QMetaObject::ReadProperty || _c == QMetaObject::WriteProperty
            || _c == QMetaObject::ResetProperty || _c == QMetaObject::RegisterPropertyMetaType) {
        qt_static_metacall(this, _c, _id, _a);
        _id -= 6;
    } else if (_c == QMetaObject::QueryPropertyDesignable) {
        _id -= 6;
    } else if (_c == QMetaObject::QueryPropertyScriptable) {
        _id -= 6;
    } else if (_c == QMetaObject::QueryPropertyStored) {
        _id -= 6;
    } else if (_c == QMetaObject::QueryPropertyEditable) {
        _id -= 6;
    } else if (_c == QMetaObject::QueryPropertyUser) {
        _id -= 6;
    }
#endif // QT_NO_PROPERTIES
    return _id;
}
struct qt_meta_stringdata_QCPLayerable_t {
    QByteArrayData data[13];
    char stringdata0[135];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_QCPLayerable_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_QCPLayerable_t qt_meta_stringdata_QCPLayerable = {
    {
QT_MOC_LITERAL(0, 0, 12), // "QCPLayerable"
QT_MOC_LITERAL(1, 13, 12), // "layerChanged"
QT_MOC_LITERAL(2, 26, 0), // ""
QT_MOC_LITERAL(3, 27, 9), // "QCPLayer*"
QT_MOC_LITERAL(4, 37, 8), // "newLayer"
QT_MOC_LITERAL(5, 46, 8), // "setLayer"
QT_MOC_LITERAL(6, 55, 5), // "layer"
QT_MOC_LITERAL(7, 61, 7), // "visible"
QT_MOC_LITERAL(8, 69, 10), // "parentPlot"
QT_MOC_LITERAL(9, 80, 12), // "QCustomPlot*"
QT_MOC_LITERAL(10, 93, 15), // "parentLayerable"
QT_MOC_LITERAL(11, 109, 13), // "QCPLayerable*"
QT_MOC_LITERAL(12, 123, 11) // "antialiased"

    },
    "QCPLayerable\0layerChanged\0\0QCPLayer*\0"
    "newLayer\0setLayer\0layer\0visible\0"
    "parentPlot\0QCustomPlot*\0parentLayerable\0"
    "QCPLayerable*\0antialiased"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_QCPLayerable[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
       2,   14, // methods
       5,   30, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       1,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    1,   24,    2, 0x06 /* Public */,

 // slots: name, argc, parameters, tag, flags
       5,    1,   27,    2, 0x0a /* Public */,

 // signals: parameters
    QMetaType::Void, 0x80000000 | 3,    4,

 // slots: parameters
    QMetaType::Bool, 0x80000000 | 3,    6,

 // properties: name, type, flags
       7, QMetaType::Bool, 0x00095103,
       8, 0x80000000 | 9, 0x00095009,
      10, 0x80000000 | 11, 0x00095009,
       6, 0x80000000 | 3, 0x0049510b,
      12, QMetaType::Bool, 0x00095103,

 // properties: notify_signal_id
       0,
       0,
       0,
       0,
       0,

       0        // eod
};

void QCPLayerable::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<QCPLayerable *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->layerChanged((*reinterpret_cast< QCPLayer*(*)>(_a[1]))); break;
        case 1: { bool _r = _t->setLayer((*reinterpret_cast< QCPLayer*(*)>(_a[1])));
            if (_a[0]) *reinterpret_cast< bool*>(_a[0]) = std::move(_r); }  break;
        default: ;
        }
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        switch (_id) {
        default: *reinterpret_cast<int*>(_a[0]) = -1; break;
        case 0:
            switch (*reinterpret_cast<int*>(_a[1])) {
            default: *reinterpret_cast<int*>(_a[0]) = -1; break;
            case 0:
                *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< QCPLayer* >(); break;
            }
            break;
        case 1:
            switch (*reinterpret_cast<int*>(_a[1])) {
            default: *reinterpret_cast<int*>(_a[0]) = -1; break;
            case 0:
                *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< QCPLayer* >(); break;
            }
            break;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (QCPLayerable::*)(QCPLayer * );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&QCPLayerable::layerChanged)) {
                *result = 0;
                return;
            }
        }
    } else if (_c == QMetaObject::RegisterPropertyMetaType) {
        switch (_id) {
        default: *reinterpret_cast<int*>(_a[0]) = -1; break;
        case 3:
            *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< QCPLayer* >(); break;
        case 2:
            *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< QCPLayerable* >(); break;
        case 1:
            *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< QCustomPlot* >(); break;
        }
    }

#ifndef QT_NO_PROPERTIES
    else if (_c == QMetaObject::ReadProperty) {
        auto *_t = static_cast<QCPLayerable *>(_o);
        Q_UNUSED(_t)
        void *_v = _a[0];
        switch (_id) {
        case 0: *reinterpret_cast< bool*>(_v) = _t->visible(); break;
        case 1: *reinterpret_cast< QCustomPlot**>(_v) = _t->parentPlot(); break;
        case 2: *reinterpret_cast< QCPLayerable**>(_v) = _t->parentLayerable(); break;
        case 3: *reinterpret_cast< QCPLayer**>(_v) = _t->layer(); break;
        case 4: *reinterpret_cast< bool*>(_v) = _t->antialiased(); break;
        default: break;
        }
    } else if (_c == QMetaObject::WriteProperty) {
        auto *_t = static_cast<QCPLayerable *>(_o);
        Q_UNUSED(_t)
        void *_v = _a[0];
        switch (_id) {
        case 0: _t->setVisible(*reinterpret_cast< bool*>(_v)); break;
        case 3: _t->setLayer(*reinterpret_cast< QCPLayer**>(_v)); break;
        case 4: _t->setAntialiased(*reinterpret_cast< bool*>(_v)); break;
        default: break;
        }
    } else if (_c == QMetaObject::ResetProperty) {
    }
#endif // QT_NO_PROPERTIES
}

QT_INIT_METAOBJECT const QMetaObject QCPLayerable::staticMetaObject = { {
    &QObject::staticMetaObject,
    qt_meta_stringdata_QCPLayerable.data,
    qt_meta_data_QCPLayerable,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *QCPLayerable::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *QCPLayerable::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_QCPLayerable.stringdata0))
        return static_cast<void*>(this);
    return QObject::qt_metacast(_clname);
}

int QCPLayerable::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QObject::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 2)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 2;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 2)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 2;
    }
#ifndef QT_NO_PROPERTIES
    else if (_c == QMetaObject::ReadProperty || _c == QMetaObject::WriteProperty
            || _c == QMetaObject::ResetProperty || _c == QMetaObject::RegisterPropertyMetaType) {
        qt_static_metacall(this, _c, _id, _a);
        _id -= 5;
    } else if (_c == QMetaObject::QueryPropertyDesignable) {
        _id -= 5;
    } else if (_c == QMetaObject::QueryPropertyScriptable) {
        _id -= 5;
    } else if (_c == QMetaObject::QueryPropertyStored) {
        _id -= 5;
    } else if (_c == QMetaObject::QueryPropertyEditable) {
        _id -= 5;
    } else if (_c == QMetaObject::QueryPropertyUser) {
        _id -= 5;
    }
#endif // QT_NO_PROPERTIES
    return _id;
}

// SIGNAL 0
void QCPLayerable::layerChanged(QCPLayer * _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}
struct qt_meta_stringdata_QCPSelectionRect_t {
    QByteArrayData data[11];
    char stringdata0[96];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_QCPSelectionRect_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_QCPSelectionRect_t qt_meta_stringdata_QCPSelectionRect = {
    {
QT_MOC_LITERAL(0, 0, 16), // "QCPSelectionRect"
QT_MOC_LITERAL(1, 17, 7), // "started"
QT_MOC_LITERAL(2, 25, 0), // ""
QT_MOC_LITERAL(3, 26, 12), // "QMouseEvent*"
QT_MOC_LITERAL(4, 39, 5), // "event"
QT_MOC_LITERAL(5, 45, 7), // "changed"
QT_MOC_LITERAL(6, 53, 4), // "rect"
QT_MOC_LITERAL(7, 58, 8), // "canceled"
QT_MOC_LITERAL(8, 67, 12), // "QInputEvent*"
QT_MOC_LITERAL(9, 80, 8), // "accepted"
QT_MOC_LITERAL(10, 89, 6) // "cancel"

    },
    "QCPSelectionRect\0started\0\0QMouseEvent*\0"
    "event\0changed\0rect\0canceled\0QInputEvent*\0"
    "accepted\0cancel"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_QCPSelectionRect[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
       5,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       4,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    1,   39,    2, 0x06 /* Public */,
       5,    2,   42,    2, 0x06 /* Public */,
       7,    2,   47,    2, 0x06 /* Public */,
       9,    2,   52,    2, 0x06 /* Public */,

 // slots: name, argc, parameters, tag, flags
      10,    0,   57,    2, 0x0a /* Public */,

 // signals: parameters
    QMetaType::Void, 0x80000000 | 3,    4,
    QMetaType::Void, QMetaType::QRect, 0x80000000 | 3,    6,    4,
    QMetaType::Void, QMetaType::QRect, 0x80000000 | 8,    6,    4,
    QMetaType::Void, QMetaType::QRect, 0x80000000 | 3,    6,    4,

 // slots: parameters
    QMetaType::Void,

       0        // eod
};

void QCPSelectionRect::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<QCPSelectionRect *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->started((*reinterpret_cast< QMouseEvent*(*)>(_a[1]))); break;
        case 1: _t->changed((*reinterpret_cast< const QRect(*)>(_a[1])),(*reinterpret_cast< QMouseEvent*(*)>(_a[2]))); break;
        case 2: _t->canceled((*reinterpret_cast< const QRect(*)>(_a[1])),(*reinterpret_cast< QInputEvent*(*)>(_a[2]))); break;
        case 3: _t->accepted((*reinterpret_cast< const QRect(*)>(_a[1])),(*reinterpret_cast< QMouseEvent*(*)>(_a[2]))); break;
        case 4: _t->cancel(); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (QCPSelectionRect::*)(QMouseEvent * );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&QCPSelectionRect::started)) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (QCPSelectionRect::*)(const QRect & , QMouseEvent * );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&QCPSelectionRect::changed)) {
                *result = 1;
                return;
            }
        }
        {
            using _t = void (QCPSelectionRect::*)(const QRect & , QInputEvent * );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&QCPSelectionRect::canceled)) {
                *result = 2;
                return;
            }
        }
        {
            using _t = void (QCPSelectionRect::*)(const QRect & , QMouseEvent * );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&QCPSelectionRect::accepted)) {
                *result = 3;
                return;
            }
        }
    }
}

QT_INIT_METAOBJECT const QMetaObject QCPSelectionRect::staticMetaObject = { {
    &QCPLayerable::staticMetaObject,
    qt_meta_stringdata_QCPSelectionRect.data,
    qt_meta_data_QCPSelectionRect,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *QCPSelectionRect::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *QCPSelectionRect::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_QCPSelectionRect.stringdata0))
        return static_cast<void*>(this);
    return QCPLayerable::qt_metacast(_clname);
}

int QCPSelectionRect::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QCPLayerable::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 5)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 5;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 5)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 5;
    }
    return _id;
}

// SIGNAL 0
void QCPSelectionRect::started(QMouseEvent * _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}

// SIGNAL 1
void QCPSelectionRect::changed(const QRect & _t1, QMouseEvent * _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))) };
    QMetaObject::activate(this, &staticMetaObject, 1, _a);
}

// SIGNAL 2
void QCPSelectionRect::canceled(const QRect & _t1, QInputEvent * _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))) };
    QMetaObject::activate(this, &staticMetaObject, 2, _a);
}

// SIGNAL 3
void QCPSelectionRect::accepted(const QRect & _t1, QMouseEvent * _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))) };
    QMetaObject::activate(this, &staticMetaObject, 3, _a);
}
struct qt_meta_stringdata_QCPMarginGroup_t {
    QByteArrayData data[1];
    char stringdata0[15];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_QCPMarginGroup_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_QCPMarginGroup_t qt_meta_stringdata_QCPMarginGroup = {
    {
QT_MOC_LITERAL(0, 0, 14) // "QCPMarginGroup"

    },
    "QCPMarginGroup"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_QCPMarginGroup[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
       0,    0, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

       0        // eod
};

void QCPMarginGroup::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    Q_UNUSED(_o);
    Q_UNUSED(_id);
    Q_UNUSED(_c);
    Q_UNUSED(_a);
}

QT_INIT_METAOBJECT const QMetaObject QCPMarginGroup::staticMetaObject = { {
    &QObject::staticMetaObject,
    qt_meta_stringdata_QCPMarginGroup.data,
    qt_meta_data_QCPMarginGroup,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *QCPMarginGroup::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *QCPMarginGroup::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_QCPMarginGroup.stringdata0))
        return static_cast<void*>(this);
    return QObject::qt_metacast(_clname);
}

int QCPMarginGroup::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QObject::qt_metacall(_c, _id, _a);
    return _id;
}
struct qt_meta_stringdata_QCPLayoutElement_t {
    QByteArrayData data[18];
    char stringdata0[215];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_QCPLayoutElement_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_QCPLayoutElement_t qt_meta_stringdata_QCPLayoutElement = {
    {
QT_MOC_LITERAL(0, 0, 16), // "QCPLayoutElement"
QT_MOC_LITERAL(1, 17, 6), // "layout"
QT_MOC_LITERAL(2, 24, 10), // "QCPLayout*"
QT_MOC_LITERAL(3, 35, 4), // "rect"
QT_MOC_LITERAL(4, 40, 9), // "outerRect"
QT_MOC_LITERAL(5, 50, 7), // "margins"
QT_MOC_LITERAL(6, 58, 8), // "QMargins"
QT_MOC_LITERAL(7, 67, 14), // "minimumMargins"
QT_MOC_LITERAL(8, 82, 11), // "minimumSize"
QT_MOC_LITERAL(9, 94, 11), // "maximumSize"
QT_MOC_LITERAL(10, 106, 18), // "sizeConstraintRect"
QT_MOC_LITERAL(11, 125, 18), // "SizeConstraintRect"
QT_MOC_LITERAL(12, 144, 11), // "UpdatePhase"
QT_MOC_LITERAL(13, 156, 13), // "upPreparation"
QT_MOC_LITERAL(14, 170, 9), // "upMargins"
QT_MOC_LITERAL(15, 180, 8), // "upLayout"
QT_MOC_LITERAL(16, 189, 12), // "scrInnerRect"
QT_MOC_LITERAL(17, 202, 12) // "scrOuterRect"

    },
    "QCPLayoutElement\0layout\0QCPLayout*\0"
    "rect\0outerRect\0margins\0QMargins\0"
    "minimumMargins\0minimumSize\0maximumSize\0"
    "sizeConstraintRect\0SizeConstraintRect\0"
    "UpdatePhase\0upPreparation\0upMargins\0"
    "upLayout\0scrInnerRect\0scrOuterRect"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_QCPLayoutElement[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
       0,    0, // methods
       8,   14, // properties
       2,   38, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

 // properties: name, type, flags
       1, 0x80000000 | 2, 0x00095009,
       3, QMetaType::QRect, 0x00095001,
       4, QMetaType::QRect, 0x00095103,
       5, 0x80000000 | 6, 0x0009510b,
       7, 0x80000000 | 6, 0x0009510b,
       8, QMetaType::QSize, 0x00095103,
       9, QMetaType::QSize, 0x00095103,
      10, 0x80000000 | 11, 0x0009510b,

 // enums: name, alias, flags, count, data
      12,   12, 0x0,    3,   48,
      11,   11, 0x0,    2,   54,

 // enum data: key, value
      13, uint(QCPLayoutElement::upPreparation),
      14, uint(QCPLayoutElement::upMargins),
      15, uint(QCPLayoutElement::upLayout),
      16, uint(QCPLayoutElement::scrInnerRect),
      17, uint(QCPLayoutElement::scrOuterRect),

       0        // eod
};

void QCPLayoutElement::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::RegisterPropertyMetaType) {
        switch (_id) {
        default: *reinterpret_cast<int*>(_a[0]) = -1; break;
        case 0:
            *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< QCPLayout* >(); break;
        }
    }

#ifndef QT_NO_PROPERTIES
    else if (_c == QMetaObject::ReadProperty) {
        auto *_t = static_cast<QCPLayoutElement *>(_o);
        Q_UNUSED(_t)
        void *_v = _a[0];
        switch (_id) {
        case 0: *reinterpret_cast< QCPLayout**>(_v) = _t->layout(); break;
        case 1: *reinterpret_cast< QRect*>(_v) = _t->rect(); break;
        case 2: *reinterpret_cast< QRect*>(_v) = _t->outerRect(); break;
        case 3: *reinterpret_cast< QMargins*>(_v) = _t->margins(); break;
        case 4: *reinterpret_cast< QMargins*>(_v) = _t->minimumMargins(); break;
        case 5: *reinterpret_cast< QSize*>(_v) = _t->minimumSize(); break;
        case 6: *reinterpret_cast< QSize*>(_v) = _t->maximumSize(); break;
        case 7: *reinterpret_cast< SizeConstraintRect*>(_v) = _t->sizeConstraintRect(); break;
        default: break;
        }
    } else if (_c == QMetaObject::WriteProperty) {
        auto *_t = static_cast<QCPLayoutElement *>(_o);
        Q_UNUSED(_t)
        void *_v = _a[0];
        switch (_id) {
        case 2: _t->setOuterRect(*reinterpret_cast< QRect*>(_v)); break;
        case 3: _t->setMargins(*reinterpret_cast< QMargins*>(_v)); break;
        case 4: _t->setMinimumMargins(*reinterpret_cast< QMargins*>(_v)); break;
        case 5: _t->setMinimumSize(*reinterpret_cast< QSize*>(_v)); break;
        case 6: _t->setMaximumSize(*reinterpret_cast< QSize*>(_v)); break;
        case 7: _t->setSizeConstraintRect(*reinterpret_cast< SizeConstraintRect*>(_v)); break;
        default: break;
        }
    } else if (_c == QMetaObject::ResetProperty) {
    }
#endif // QT_NO_PROPERTIES
    Q_UNUSED(_o);
}

QT_INIT_METAOBJECT const QMetaObject QCPLayoutElement::staticMetaObject = { {
    &QCPLayerable::staticMetaObject,
    qt_meta_stringdata_QCPLayoutElement.data,
    qt_meta_data_QCPLayoutElement,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *QCPLayoutElement::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *QCPLayoutElement::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_QCPLayoutElement.stringdata0))
        return static_cast<void*>(this);
    return QCPLayerable::qt_metacast(_clname);
}

int QCPLayoutElement::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QCPLayerable::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    
#ifndef QT_NO_PROPERTIES
    if (_c == QMetaObject::ReadProperty || _c == QMetaObject::WriteProperty
            || _c == QMetaObject::ResetProperty || _c == QMetaObject::RegisterPropertyMetaType) {
        qt_static_metacall(this, _c, _id, _a);
        _id -= 8;
    } else if (_c == QMetaObject::QueryPropertyDesignable) {
        _id -= 8;
    } else if (_c == QMetaObject::QueryPropertyScriptable) {
        _id -= 8;
    } else if (_c == QMetaObject::QueryPropertyStored) {
        _id -= 8;
    } else if (_c == QMetaObject::QueryPropertyEditable) {
        _id -= 8;
    } else if (_c == QMetaObject::QueryPropertyUser) {
        _id -= 8;
    }
#endif // QT_NO_PROPERTIES
    return _id;
}
struct qt_meta_stringdata_QCPLayout_t {
    QByteArrayData data[1];
    char stringdata0[10];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_QCPLayout_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_QCPLayout_t qt_meta_stringdata_QCPLayout = {
    {
QT_MOC_LITERAL(0, 0, 9) // "QCPLayout"

    },
    "QCPLayout"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_QCPLayout[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
       0,    0, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

       0        // eod
};

void QCPLayout::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    Q_UNUSED(_o);
    Q_UNUSED(_id);
    Q_UNUSED(_c);
    Q_UNUSED(_a);
}

QT_INIT_METAOBJECT const QMetaObject QCPLayout::staticMetaObject = { {
    &QCPLayoutElement::staticMetaObject,
    qt_meta_stringdata_QCPLayout.data,
    qt_meta_data_QCPLayout,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *QCPLayout::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *QCPLayout::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_QCPLayout.stringdata0))
        return static_cast<void*>(this);
    return QCPLayoutElement::qt_metacast(_clname);
}

int QCPLayout::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QCPLayoutElement::qt_metacall(_c, _id, _a);
    return _id;
}
struct qt_meta_stringdata_QCPLayoutGrid_t {
    QByteArrayData data[13];
    char stringdata0[165];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_QCPLayoutGrid_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_QCPLayoutGrid_t qt_meta_stringdata_QCPLayoutGrid = {
    {
QT_MOC_LITERAL(0, 0, 13), // "QCPLayoutGrid"
QT_MOC_LITERAL(1, 14, 8), // "rowCount"
QT_MOC_LITERAL(2, 23, 11), // "columnCount"
QT_MOC_LITERAL(3, 35, 20), // "columnStretchFactors"
QT_MOC_LITERAL(4, 56, 13), // "QList<double>"
QT_MOC_LITERAL(5, 70, 17), // "rowStretchFactors"
QT_MOC_LITERAL(6, 88, 13), // "columnSpacing"
QT_MOC_LITERAL(7, 102, 10), // "rowSpacing"
QT_MOC_LITERAL(8, 113, 9), // "fillOrder"
QT_MOC_LITERAL(9, 123, 9), // "FillOrder"
QT_MOC_LITERAL(10, 133, 4), // "wrap"
QT_MOC_LITERAL(11, 138, 11), // "foRowsFirst"
QT_MOC_LITERAL(12, 150, 14) // "foColumnsFirst"

    },
    "QCPLayoutGrid\0rowCount\0columnCount\0"
    "columnStretchFactors\0QList<double>\0"
    "rowStretchFactors\0columnSpacing\0"
    "rowSpacing\0fillOrder\0FillOrder\0wrap\0"
    "foRowsFirst\0foColumnsFirst"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_QCPLayoutGrid[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
       0,    0, // methods
       8,   14, // properties
       1,   38, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

 // properties: name, type, flags
       1, QMetaType::Int, 0x00095001,
       2, QMetaType::Int, 0x00095001,
       3, 0x80000000 | 4, 0x0009510b,
       5, 0x80000000 | 4, 0x0009510b,
       6, QMetaType::Int, 0x00095103,
       7, QMetaType::Int, 0x00095103,
       8, 0x80000000 | 9, 0x0009510b,
      10, QMetaType::Int, 0x00095103,

 // enums: name, alias, flags, count, data
       9,    9, 0x0,    2,   43,

 // enum data: key, value
      11, uint(QCPLayoutGrid::foRowsFirst),
      12, uint(QCPLayoutGrid::foColumnsFirst),

       0        // eod
};

void QCPLayoutGrid::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::RegisterPropertyMetaType) {
        switch (_id) {
        default: *reinterpret_cast<int*>(_a[0]) = -1; break;
        case 3:
        case 2:
            *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< QList<double> >(); break;
        }
    }

#ifndef QT_NO_PROPERTIES
    else if (_c == QMetaObject::ReadProperty) {
        auto *_t = static_cast<QCPLayoutGrid *>(_o);
        Q_UNUSED(_t)
        void *_v = _a[0];
        switch (_id) {
        case 0: *reinterpret_cast< int*>(_v) = _t->rowCount(); break;
        case 1: *reinterpret_cast< int*>(_v) = _t->columnCount(); break;
        case 2: *reinterpret_cast< QList<double>*>(_v) = _t->columnStretchFactors(); break;
        case 3: *reinterpret_cast< QList<double>*>(_v) = _t->rowStretchFactors(); break;
        case 4: *reinterpret_cast< int*>(_v) = _t->columnSpacing(); break;
        case 5: *reinterpret_cast< int*>(_v) = _t->rowSpacing(); break;
        case 6: *reinterpret_cast< FillOrder*>(_v) = _t->fillOrder(); break;
        case 7: *reinterpret_cast< int*>(_v) = _t->wrap(); break;
        default: break;
        }
    } else if (_c == QMetaObject::WriteProperty) {
        auto *_t = static_cast<QCPLayoutGrid *>(_o);
        Q_UNUSED(_t)
        void *_v = _a[0];
        switch (_id) {
        case 2: _t->setColumnStretchFactors(*reinterpret_cast< QList<double>*>(_v)); break;
        case 3: _t->setRowStretchFactors(*reinterpret_cast< QList<double>*>(_v)); break;
        case 4: _t->setColumnSpacing(*reinterpret_cast< int*>(_v)); break;
        case 5: _t->setRowSpacing(*reinterpret_cast< int*>(_v)); break;
        case 6: _t->setFillOrder(*reinterpret_cast< FillOrder*>(_v)); break;
        case 7: _t->setWrap(*reinterpret_cast< int*>(_v)); break;
        default: break;
        }
    } else if (_c == QMetaObject::ResetProperty) {
    }
#endif // QT_NO_PROPERTIES
    Q_UNUSED(_o);
}

QT_INIT_METAOBJECT const QMetaObject QCPLayoutGrid::staticMetaObject = { {
    &QCPLayout::staticMetaObject,
    qt_meta_stringdata_QCPLayoutGrid.data,
    qt_meta_data_QCPLayoutGrid,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *QCPLayoutGrid::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *QCPLayoutGrid::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_QCPLayoutGrid.stringdata0))
        return static_cast<void*>(this);
    return QCPLayout::qt_metacast(_clname);
}

int QCPLayoutGrid::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QCPLayout::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    
#ifndef QT_NO_PROPERTIES
    if (_c == QMetaObject::ReadProperty || _c == QMetaObject::WriteProperty
            || _c == QMetaObject::ResetProperty || _c == QMetaObject::RegisterPropertyMetaType) {
        qt_static_metacall(this, _c, _id, _a);
        _id -= 8;
    } else if (_c == QMetaObject::QueryPropertyDesignable) {
        _id -= 8;
    } else if (_c == QMetaObject::QueryPropertyScriptable) {
        _id -= 8;
    } else if (_c == QMetaObject::QueryPropertyStored) {
        _id -= 8;
    } else if (_c == QMetaObject::QueryPropertyEditable) {
        _id -= 8;
    } else if (_c == QMetaObject::QueryPropertyUser) {
        _id -= 8;
    }
#endif // QT_NO_PROPERTIES
    return _id;
}
struct qt_meta_stringdata_QCPLayoutInset_t {
    QByteArrayData data[4];
    char stringdata0[53];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_QCPLayoutInset_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_QCPLayoutInset_t qt_meta_stringdata_QCPLayoutInset = {
    {
QT_MOC_LITERAL(0, 0, 14), // "QCPLayoutInset"
QT_MOC_LITERAL(1, 15, 14), // "InsetPlacement"
QT_MOC_LITERAL(2, 30, 6), // "ipFree"
QT_MOC_LITERAL(3, 37, 15) // "ipBorderAligned"

    },
    "QCPLayoutInset\0InsetPlacement\0ipFree\0"
    "ipBorderAligned"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_QCPLayoutInset[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
       0,    0, // methods
       0,    0, // properties
       1,   14, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

 // enums: name, alias, flags, count, data
       1,    1, 0x0,    2,   19,

 // enum data: key, value
       2, uint(QCPLayoutInset::ipFree),
       3, uint(QCPLayoutInset::ipBorderAligned),

       0        // eod
};

void QCPLayoutInset::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    Q_UNUSED(_o);
    Q_UNUSED(_id);
    Q_UNUSED(_c);
    Q_UNUSED(_a);
}

QT_INIT_METAOBJECT const QMetaObject QCPLayoutInset::staticMetaObject = { {
    &QCPLayout::staticMetaObject,
    qt_meta_stringdata_QCPLayoutInset.data,
    qt_meta_data_QCPLayoutInset,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *QCPLayoutInset::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *QCPLayoutInset::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_QCPLayoutInset.stringdata0))
        return static_cast<void*>(this);
    return QCPLayout::qt_metacast(_clname);
}

int QCPLayoutInset::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QCPLayout::qt_metacall(_c, _id, _a);
    return _id;
}
struct qt_meta_stringdata_QCPLineEnding_t {
    QByteArrayData data[12];
    char stringdata0[124];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_QCPLineEnding_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_QCPLineEnding_t qt_meta_stringdata_QCPLineEnding = {
    {
QT_MOC_LITERAL(0, 0, 13), // "QCPLineEnding"
QT_MOC_LITERAL(1, 14, 11), // "EndingStyle"
QT_MOC_LITERAL(2, 26, 6), // "esNone"
QT_MOC_LITERAL(3, 33, 11), // "esFlatArrow"
QT_MOC_LITERAL(4, 45, 12), // "esSpikeArrow"
QT_MOC_LITERAL(5, 58, 11), // "esLineArrow"
QT_MOC_LITERAL(6, 70, 6), // "esDisc"
QT_MOC_LITERAL(7, 77, 8), // "esSquare"
QT_MOC_LITERAL(8, 86, 9), // "esDiamond"
QT_MOC_LITERAL(9, 96, 5), // "esBar"
QT_MOC_LITERAL(10, 102, 9), // "esHalfBar"
QT_MOC_LITERAL(11, 112, 11) // "esSkewedBar"

    },
    "QCPLineEnding\0EndingStyle\0esNone\0"
    "esFlatArrow\0esSpikeArrow\0esLineArrow\0"
    "esDisc\0esSquare\0esDiamond\0esBar\0"
    "esHalfBar\0esSkewedBar"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_QCPLineEnding[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
       0,    0, // methods
       0,    0, // properties
       1,   14, // enums/sets
       0,    0, // constructors
       4,       // flags
       0,       // signalCount

 // enums: name, alias, flags, count, data
       1,    1, 0x0,   10,   19,

 // enum data: key, value
       2, uint(QCPLineEnding::esNone),
       3, uint(QCPLineEnding::esFlatArrow),
       4, uint(QCPLineEnding::esSpikeArrow),
       5, uint(QCPLineEnding::esLineArrow),
       6, uint(QCPLineEnding::esDisc),
       7, uint(QCPLineEnding::esSquare),
       8, uint(QCPLineEnding::esDiamond),
       9, uint(QCPLineEnding::esBar),
      10, uint(QCPLineEnding::esHalfBar),
      11, uint(QCPLineEnding::esSkewedBar),

       0        // eod
};

QT_INIT_METAOBJECT const QMetaObject QCPLineEnding::staticMetaObject = { {
    nullptr,
    qt_meta_stringdata_QCPLineEnding.data,
    qt_meta_data_QCPLineEnding,
    nullptr,
    nullptr,
    nullptr
} };

struct qt_meta_stringdata_QCPAxisTicker_t {
    QByteArrayData data[4];
    char stringdata0[63];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_QCPAxisTicker_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_QCPAxisTicker_t qt_meta_stringdata_QCPAxisTicker = {
    {
QT_MOC_LITERAL(0, 0, 13), // "QCPAxisTicker"
QT_MOC_LITERAL(1, 14, 16), // "TickStepStrategy"
QT_MOC_LITERAL(2, 31, 14), // "tssReadability"
QT_MOC_LITERAL(3, 46, 16) // "tssMeetTickCount"

    },
    "QCPAxisTicker\0TickStepStrategy\0"
    "tssReadability\0tssMeetTickCount"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_QCPAxisTicker[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
       0,    0, // methods
       0,    0, // properties
       1,   14, // enums/sets
       0,    0, // constructors
       4,       // flags
       0,       // signalCount

 // enums: name, alias, flags, count, data
       1,    1, 0x0,    2,   19,

 // enum data: key, value
       2, uint(QCPAxisTicker::tssReadability),
       3, uint(QCPAxisTicker::tssMeetTickCount),

       0        // eod
};

QT_INIT_METAOBJECT const QMetaObject QCPAxisTicker::staticMetaObject = { {
    nullptr,
    qt_meta_stringdata_QCPAxisTicker.data,
    qt_meta_data_QCPAxisTicker,
    nullptr,
    nullptr,
    nullptr
} };

struct qt_meta_stringdata_QCPAxisTickerTime_t {
    QByteArrayData data[7];
    char stringdata0[77];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_QCPAxisTickerTime_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_QCPAxisTickerTime_t qt_meta_stringdata_QCPAxisTickerTime = {
    {
QT_MOC_LITERAL(0, 0, 17), // "QCPAxisTickerTime"
QT_MOC_LITERAL(1, 18, 8), // "TimeUnit"
QT_MOC_LITERAL(2, 27, 14), // "tuMilliseconds"
QT_MOC_LITERAL(3, 42, 9), // "tuSeconds"
QT_MOC_LITERAL(4, 52, 9), // "tuMinutes"
QT_MOC_LITERAL(5, 62, 7), // "tuHours"
QT_MOC_LITERAL(6, 70, 6) // "tuDays"

    },
    "QCPAxisTickerTime\0TimeUnit\0tuMilliseconds\0"
    "tuSeconds\0tuMinutes\0tuHours\0tuDays"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_QCPAxisTickerTime[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
       0,    0, // methods
       0,    0, // properties
       1,   14, // enums/sets
       0,    0, // constructors
       4,       // flags
       0,       // signalCount

 // enums: name, alias, flags, count, data
       1,    1, 0x0,    5,   19,

 // enum data: key, value
       2, uint(QCPAxisTickerTime::tuMilliseconds),
       3, uint(QCPAxisTickerTime::tuSeconds),
       4, uint(QCPAxisTickerTime::tuMinutes),
       5, uint(QCPAxisTickerTime::tuHours),
       6, uint(QCPAxisTickerTime::tuDays),

       0        // eod
};

QT_INIT_METAOBJECT const QMetaObject QCPAxisTickerTime::staticMetaObject = { {
    &QCPAxisTicker::staticMetaObject,
    qt_meta_stringdata_QCPAxisTickerTime.data,
    qt_meta_data_QCPAxisTickerTime,
    nullptr,
    nullptr,
    nullptr
} };

struct qt_meta_stringdata_QCPAxisTickerFixed_t {
    QByteArrayData data[5];
    char stringdata0[61];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_QCPAxisTickerFixed_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_QCPAxisTickerFixed_t qt_meta_stringdata_QCPAxisTickerFixed = {
    {
QT_MOC_LITERAL(0, 0, 18), // "QCPAxisTickerFixed"
QT_MOC_LITERAL(1, 19, 13), // "ScaleStrategy"
QT_MOC_LITERAL(2, 33, 6), // "ssNone"
QT_MOC_LITERAL(3, 40, 11), // "ssMultiples"
QT_MOC_LITERAL(4, 52, 8) // "ssPowers"

    },
    "QCPAxisTickerFixed\0ScaleStrategy\0"
    "ssNone\0ssMultiples\0ssPowers"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_QCPAxisTickerFixed[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
       0,    0, // methods
       0,    0, // properties
       1,   14, // enums/sets
       0,    0, // constructors
       4,       // flags
       0,       // signalCount

 // enums: name, alias, flags, count, data
       1,    1, 0x0,    3,   19,

 // enum data: key, value
       2, uint(QCPAxisTickerFixed::ssNone),
       3, uint(QCPAxisTickerFixed::ssMultiples),
       4, uint(QCPAxisTickerFixed::ssPowers),

       0        // eod
};

QT_INIT_METAOBJECT const QMetaObject QCPAxisTickerFixed::staticMetaObject = { {
    &QCPAxisTicker::staticMetaObject,
    qt_meta_stringdata_QCPAxisTickerFixed.data,
    qt_meta_data_QCPAxisTickerFixed,
    nullptr,
    nullptr,
    nullptr
} };

struct qt_meta_stringdata_QCPAxisTickerPi_t {
    QByteArrayData data[5];
    char stringdata0[82];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_QCPAxisTickerPi_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_QCPAxisTickerPi_t qt_meta_stringdata_QCPAxisTickerPi = {
    {
QT_MOC_LITERAL(0, 0, 15), // "QCPAxisTickerPi"
QT_MOC_LITERAL(1, 16, 13), // "FractionStyle"
QT_MOC_LITERAL(2, 30, 15), // "fsFloatingPoint"
QT_MOC_LITERAL(3, 46, 16), // "fsAsciiFractions"
QT_MOC_LITERAL(4, 63, 18) // "fsUnicodeFractions"

    },
    "QCPAxisTickerPi\0FractionStyle\0"
    "fsFloatingPoint\0fsAsciiFractions\0"
    "fsUnicodeFractions"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_QCPAxisTickerPi[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
       0,    0, // methods
       0,    0, // properties
       1,   14, // enums/sets
       0,    0, // constructors
       4,       // flags
       0,       // signalCount

 // enums: name, alias, flags, count, data
       1,    1, 0x0,    3,   19,

 // enum data: key, value
       2, uint(QCPAxisTickerPi::fsFloatingPoint),
       3, uint(QCPAxisTickerPi::fsAsciiFractions),
       4, uint(QCPAxisTickerPi::fsUnicodeFractions),

       0        // eod
};

QT_INIT_METAOBJECT const QMetaObject QCPAxisTickerPi::staticMetaObject = { {
    &QCPAxisTicker::staticMetaObject,
    qt_meta_stringdata_QCPAxisTickerPi.data,
    qt_meta_data_QCPAxisTickerPi,
    nullptr,
    nullptr,
    nullptr
} };

struct qt_meta_stringdata_QCPGrid_t {
    QByteArrayData data[7];
    char stringdata0[89];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_QCPGrid_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_QCPGrid_t qt_meta_stringdata_QCPGrid = {
    {
QT_MOC_LITERAL(0, 0, 7), // "QCPGrid"
QT_MOC_LITERAL(1, 8, 14), // "subGridVisible"
QT_MOC_LITERAL(2, 23, 18), // "antialiasedSubGrid"
QT_MOC_LITERAL(3, 42, 19), // "antialiasedZeroLine"
QT_MOC_LITERAL(4, 62, 3), // "pen"
QT_MOC_LITERAL(5, 66, 10), // "subGridPen"
QT_MOC_LITERAL(6, 77, 11) // "zeroLinePen"

    },
    "QCPGrid\0subGridVisible\0antialiasedSubGrid\0"
    "antialiasedZeroLine\0pen\0subGridPen\0"
    "zeroLinePen"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_QCPGrid[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
       0,    0, // methods
       6,   14, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

 // properties: name, type, flags
       1, QMetaType::Bool, 0x00095103,
       2, QMetaType::Bool, 0x00095103,
       3, QMetaType::Bool, 0x00095103,
       4, QMetaType::QPen, 0x00095103,
       5, QMetaType::QPen, 0x00095103,
       6, QMetaType::QPen, 0x00095103,

       0        // eod
};

void QCPGrid::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{

#ifndef QT_NO_PROPERTIES
    if (_c == QMetaObject::ReadProperty) {
        auto *_t = static_cast<QCPGrid *>(_o);
        Q_UNUSED(_t)
        void *_v = _a[0];
        switch (_id) {
        case 0: *reinterpret_cast< bool*>(_v) = _t->subGridVisible(); break;
        case 1: *reinterpret_cast< bool*>(_v) = _t->antialiasedSubGrid(); break;
        case 2: *reinterpret_cast< bool*>(_v) = _t->antialiasedZeroLine(); break;
        case 3: *reinterpret_cast< QPen*>(_v) = _t->pen(); break;
        case 4: *reinterpret_cast< QPen*>(_v) = _t->subGridPen(); break;
        case 5: *reinterpret_cast< QPen*>(_v) = _t->zeroLinePen(); break;
        default: break;
        }
    } else if (_c == QMetaObject::WriteProperty) {
        auto *_t = static_cast<QCPGrid *>(_o);
        Q_UNUSED(_t)
        void *_v = _a[0];
        switch (_id) {
        case 0: _t->setSubGridVisible(*reinterpret_cast< bool*>(_v)); break;
        case 1: _t->setAntialiasedSubGrid(*reinterpret_cast< bool*>(_v)); break;
        case 2: _t->setAntialiasedZeroLine(*reinterpret_cast< bool*>(_v)); break;
        case 3: _t->setPen(*reinterpret_cast< QPen*>(_v)); break;
        case 4: _t->setSubGridPen(*reinterpret_cast< QPen*>(_v)); break;
        case 5: _t->setZeroLinePen(*reinterpret_cast< QPen*>(_v)); break;
        default: break;
        }
    } else if (_c == QMetaObject::ResetProperty) {
    }
#endif // QT_NO_PROPERTIES
    Q_UNUSED(_o);
    Q_UNUSED(_id);
    Q_UNUSED(_c);
    Q_UNUSED(_a);
}

QT_INIT_METAOBJECT const QMetaObject QCPGrid::staticMetaObject = { {
    &QCPLayerable::staticMetaObject,
    qt_meta_stringdata_QCPGrid.data,
    qt_meta_data_QCPGrid,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *QCPGrid::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *QCPGrid::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_QCPGrid.stringdata0))
        return static_cast<void*>(this);
    return QCPLayerable::qt_metacast(_clname);
}

int QCPGrid::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QCPLayerable::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    
#ifndef QT_NO_PROPERTIES
    if (_c == QMetaObject::ReadProperty || _c == QMetaObject::WriteProperty
            || _c == QMetaObject::ResetProperty || _c == QMetaObject::RegisterPropertyMetaType) {
        qt_static_metacall(this, _c, _id, _a);
        _id -= 6;
    } else if (_c == QMetaObject::QueryPropertyDesignable) {
        _id -= 6;
    } else if (_c == QMetaObject::QueryPropertyScriptable) {
        _id -= 6;
    } else if (_c == QMetaObject::QueryPropertyStored) {
        _id -= 6;
    } else if (_c == QMetaObject::QueryPropertyEditable) {
        _id -= 6;
    } else if (_c == QMetaObject::QueryPropertyUser) {
        _id -= 6;
    }
#endif // QT_NO_PROPERTIES
    return _id;
}
struct qt_meta_stringdata_QCPAxis_t {
    QByteArrayData data[84];
    char stringdata0[1044];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_QCPAxis_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_QCPAxis_t qt_meta_stringdata_QCPAxis = {
    {
QT_MOC_LITERAL(0, 0, 7), // "QCPAxis"
QT_MOC_LITERAL(1, 8, 12), // "rangeChanged"
QT_MOC_LITERAL(2, 21, 0), // ""
QT_MOC_LITERAL(3, 22, 8), // "QCPRange"
QT_MOC_LITERAL(4, 31, 8), // "newRange"
QT_MOC_LITERAL(5, 40, 8), // "oldRange"
QT_MOC_LITERAL(6, 49, 16), // "scaleTypeChanged"
QT_MOC_LITERAL(7, 66, 18), // "QCPAxis::ScaleType"
QT_MOC_LITERAL(8, 85, 9), // "scaleType"
QT_MOC_LITERAL(9, 95, 16), // "selectionChanged"
QT_MOC_LITERAL(10, 112, 24), // "QCPAxis::SelectableParts"
QT_MOC_LITERAL(11, 137, 5), // "parts"
QT_MOC_LITERAL(12, 143, 17), // "selectableChanged"
QT_MOC_LITERAL(13, 161, 12), // "setScaleType"
QT_MOC_LITERAL(14, 174, 4), // "type"
QT_MOC_LITERAL(15, 179, 8), // "setRange"
QT_MOC_LITERAL(16, 188, 5), // "range"
QT_MOC_LITERAL(17, 194, 18), // "setSelectableParts"
QT_MOC_LITERAL(18, 213, 15), // "selectableParts"
QT_MOC_LITERAL(19, 229, 16), // "setSelectedParts"
QT_MOC_LITERAL(20, 246, 13), // "selectedParts"
QT_MOC_LITERAL(21, 260, 8), // "axisType"
QT_MOC_LITERAL(22, 269, 8), // "AxisType"
QT_MOC_LITERAL(23, 278, 8), // "axisRect"
QT_MOC_LITERAL(24, 287, 12), // "QCPAxisRect*"
QT_MOC_LITERAL(25, 300, 9), // "ScaleType"
QT_MOC_LITERAL(26, 310, 13), // "rangeReversed"
QT_MOC_LITERAL(27, 324, 6), // "ticker"
QT_MOC_LITERAL(28, 331, 29), // "QSharedPointer<QCPAxisTicker>"
QT_MOC_LITERAL(29, 361, 5), // "ticks"
QT_MOC_LITERAL(30, 367, 10), // "tickLabels"
QT_MOC_LITERAL(31, 378, 16), // "tickLabelPadding"
QT_MOC_LITERAL(32, 395, 13), // "tickLabelFont"
QT_MOC_LITERAL(33, 409, 14), // "tickLabelColor"
QT_MOC_LITERAL(34, 424, 17), // "tickLabelRotation"
QT_MOC_LITERAL(35, 442, 13), // "tickLabelSide"
QT_MOC_LITERAL(36, 456, 9), // "LabelSide"
QT_MOC_LITERAL(37, 466, 12), // "numberFormat"
QT_MOC_LITERAL(38, 479, 15), // "numberPrecision"
QT_MOC_LITERAL(39, 495, 10), // "tickVector"
QT_MOC_LITERAL(40, 506, 15), // "QVector<double>"
QT_MOC_LITERAL(41, 522, 16), // "tickVectorLabels"
QT_MOC_LITERAL(42, 539, 16), // "QVector<QString>"
QT_MOC_LITERAL(43, 556, 12), // "tickLengthIn"
QT_MOC_LITERAL(44, 569, 13), // "tickLengthOut"
QT_MOC_LITERAL(45, 583, 8), // "subTicks"
QT_MOC_LITERAL(46, 592, 15), // "subTickLengthIn"
QT_MOC_LITERAL(47, 608, 16), // "subTickLengthOut"
QT_MOC_LITERAL(48, 625, 7), // "basePen"
QT_MOC_LITERAL(49, 633, 7), // "tickPen"
QT_MOC_LITERAL(50, 641, 10), // "subTickPen"
QT_MOC_LITERAL(51, 652, 9), // "labelFont"
QT_MOC_LITERAL(52, 662, 10), // "labelColor"
QT_MOC_LITERAL(53, 673, 5), // "label"
QT_MOC_LITERAL(54, 679, 12), // "labelPadding"
QT_MOC_LITERAL(55, 692, 7), // "padding"
QT_MOC_LITERAL(56, 700, 6), // "offset"
QT_MOC_LITERAL(57, 707, 15), // "SelectableParts"
QT_MOC_LITERAL(58, 723, 21), // "selectedTickLabelFont"
QT_MOC_LITERAL(59, 745, 17), // "selectedLabelFont"
QT_MOC_LITERAL(60, 763, 22), // "selectedTickLabelColor"
QT_MOC_LITERAL(61, 786, 18), // "selectedLabelColor"
QT_MOC_LITERAL(62, 805, 15), // "selectedBasePen"
QT_MOC_LITERAL(63, 821, 15), // "selectedTickPen"
QT_MOC_LITERAL(64, 837, 18), // "selectedSubTickPen"
QT_MOC_LITERAL(65, 856, 11), // "lowerEnding"
QT_MOC_LITERAL(66, 868, 13), // "QCPLineEnding"
QT_MOC_LITERAL(67, 882, 11), // "upperEnding"
QT_MOC_LITERAL(68, 894, 4), // "grid"
QT_MOC_LITERAL(69, 899, 8), // "QCPGrid*"
QT_MOC_LITERAL(70, 908, 6), // "atLeft"
QT_MOC_LITERAL(71, 915, 7), // "atRight"
QT_MOC_LITERAL(72, 923, 5), // "atTop"
QT_MOC_LITERAL(73, 929, 8), // "atBottom"
QT_MOC_LITERAL(74, 938, 9), // "AxisTypes"
QT_MOC_LITERAL(75, 948, 8), // "lsInside"
QT_MOC_LITERAL(76, 957, 9), // "lsOutside"
QT_MOC_LITERAL(77, 967, 8), // "stLinear"
QT_MOC_LITERAL(78, 976, 13), // "stLogarithmic"
QT_MOC_LITERAL(79, 990, 14), // "SelectablePart"
QT_MOC_LITERAL(80, 1005, 6), // "spNone"
QT_MOC_LITERAL(81, 1012, 6), // "spAxis"
QT_MOC_LITERAL(82, 1019, 12), // "spTickLabels"
QT_MOC_LITERAL(83, 1032, 11) // "spAxisLabel"

    },
    "QCPAxis\0rangeChanged\0\0QCPRange\0newRange\0"
    "oldRange\0scaleTypeChanged\0QCPAxis::ScaleType\0"
    "scaleType\0selectionChanged\0"
    "QCPAxis::SelectableParts\0parts\0"
    "selectableChanged\0setScaleType\0type\0"
    "setRange\0range\0setSelectableParts\0"
    "selectableParts\0setSelectedParts\0"
    "selectedParts\0axisType\0AxisType\0"
    "axisRect\0QCPAxisRect*\0ScaleType\0"
    "rangeReversed\0ticker\0QSharedPointer<QCPAxisTicker>\0"
    "ticks\0tickLabels\0tickLabelPadding\0"
    "tickLabelFont\0tickLabelColor\0"
    "tickLabelRotation\0tickLabelSide\0"
    "LabelSide\0numberFormat\0numberPrecision\0"
    "tickVector\0QVector<double>\0tickVectorLabels\0"
    "QVector<QString>\0tickLengthIn\0"
    "tickLengthOut\0subTicks\0subTickLengthIn\0"
    "subTickLengthOut\0basePen\0tickPen\0"
    "subTickPen\0labelFont\0labelColor\0label\0"
    "labelPadding\0padding\0offset\0SelectableParts\0"
    "selectedTickLabelFont\0selectedLabelFont\0"
    "selectedTickLabelColor\0selectedLabelColor\0"
    "selectedBasePen\0selectedTickPen\0"
    "selectedSubTickPen\0lowerEnding\0"
    "QCPLineEnding\0upperEnding\0grid\0QCPGrid*\0"
    "atLeft\0atRight\0atTop\0atBottom\0AxisTypes\0"
    "lsInside\0lsOutside\0stLinear\0stLogarithmic\0"
    "SelectablePart\0spNone\0spAxis\0spTickLabels\0"
    "spAxisLabel"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_QCPAxis[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
       9,   14, // methods
      43,   88, // properties
       6,  260, // enums/sets
       0,    0, // constructors
       0,       // flags
       5,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    1,   59,    2, 0x06 /* Public */,
       1,    2,   62,    2, 0x06 /* Public */,
       6,    1,   67,    2, 0x06 /* Public */,
       9,    1,   70,    2, 0x06 /* Public */,
      12,    1,   73,    2, 0x06 /* Public */,

 // slots: name, argc, parameters, tag, flags
      13,    1,   76,    2, 0x0a /* Public */,
      15,    1,   79,    2, 0x0a /* Public */,
      17,    1,   82,    2, 0x0a /* Public */,
      19,    1,   85,    2, 0x0a /* Public */,

 // signals: parameters
    QMetaType::Void, 0x80000000 | 3,    4,
    QMetaType::Void, 0x80000000 | 3, 0x80000000 | 3,    4,    5,
    QMetaType::Void, 0x80000000 | 7,    8,
    QMetaType::Void, 0x80000000 | 10,   11,
    QMetaType::Void, 0x80000000 | 10,   11,

 // slots: parameters
    QMetaType::Void, 0x80000000 | 7,   14,
    QMetaType::Void, 0x80000000 | 3,   16,
    QMetaType::Void, 0x80000000 | 10,   18,
    QMetaType::Void, 0x80000000 | 10,   20,

 // properties: name, type, flags
      21, 0x80000000 | 22, 0x00095009,
      23, 0x80000000 | 24, 0x00095009,
       8, 0x80000000 | 25, 0x0049510b,
      16, 0x80000000 | 3, 0x0049510b,
      26, QMetaType::Bool, 0x00095103,
      27, 0x80000000 | 28, 0x0009510b,
      29, QMetaType::Bool, 0x00095103,
      30, QMetaType::Bool, 0x00095103,
      31, QMetaType::Int, 0x00095103,
      32, QMetaType::QFont, 0x00095103,
      33, QMetaType::QColor, 0x00095103,
      34, QMetaType::Double, 0x00095103,
      35, 0x80000000 | 36, 0x0009510b,
      37, QMetaType::QString, 0x00095103,
      38, QMetaType::Int, 0x00095103,
      39, 0x80000000 | 40, 0x00095009,
      41, 0x80000000 | 42, 0x00095009,
      43, QMetaType::Int, 0x00095103,
      44, QMetaType::Int, 0x00095103,
      45, QMetaType::Bool, 0x00095103,
      46, QMetaType::Int, 0x00095103,
      47, QMetaType::Int, 0x00095103,
      48, QMetaType::QPen, 0x00095103,
      49, QMetaType::QPen, 0x00095103,
      50, QMetaType::QPen, 0x00095103,
      51, QMetaType::QFont, 0x00095103,
      52, QMetaType::QColor, 0x00095103,
      53, QMetaType::QString, 0x00095103,
      54, QMetaType::Int, 0x00095103,
      55, QMetaType::Int, 0x00095103,
      56, QMetaType::Int, 0x00095103,
      20, 0x80000000 | 57, 0x0049510b,
      18, 0x80000000 | 57, 0x0049510b,
      58, QMetaType::QFont, 0x00095103,
      59, QMetaType::QFont, 0x00095103,
      60, QMetaType::QColor, 0x00095103,
      61, QMetaType::QColor, 0x00095103,
      62, QMetaType::QPen, 0x00095103,
      63, QMetaType::QPen, 0x00095103,
      64, QMetaType::QPen, 0x00095103,
      65, 0x80000000 | 66, 0x0009510b,
      67, 0x80000000 | 66, 0x0009510b,
      68, 0x80000000 | 69, 0x00095009,

 // properties: notify_signal_id
       0,
       0,
       2,
       0,
       0,
       0,
       0,
       0,
       0,
       0,
       0,
       0,
       0,
       0,
       0,
       0,
       0,
       0,
       0,
       0,
       0,
       0,
       0,
       0,
       0,
       0,
       0,
       0,
       0,
       0,
       0,
       3,
       4,
       0,
       0,
       0,
       0,
       0,
       0,
       0,
       0,
       0,
       0,

 // enums: name, alias, flags, count, data
      22,   22, 0x0,    4,  290,
      74,   22, 0x1,    4,  298,
      36,   36, 0x0,    2,  306,
      25,   25, 0x0,    2,  310,
      79,   79, 0x0,    4,  314,
      57,   79, 0x1,    4,  322,

 // enum data: key, value
      70, uint(QCPAxis::atLeft),
      71, uint(QCPAxis::atRight),
      72, uint(QCPAxis::atTop),
      73, uint(QCPAxis::atBottom),
      70, uint(QCPAxis::atLeft),
      71, uint(QCPAxis::atRight),
      72, uint(QCPAxis::atTop),
      73, uint(QCPAxis::atBottom),
      75, uint(QCPAxis::lsInside),
      76, uint(QCPAxis::lsOutside),
      77, uint(QCPAxis::stLinear),
      78, uint(QCPAxis::stLogarithmic),
      80, uint(QCPAxis::spNone),
      81, uint(QCPAxis::spAxis),
      82, uint(QCPAxis::spTickLabels),
      83, uint(QCPAxis::spAxisLabel),
      80, uint(QCPAxis::spNone),
      81, uint(QCPAxis::spAxis),
      82, uint(QCPAxis::spTickLabels),
      83, uint(QCPAxis::spAxisLabel),

       0        // eod
};

void QCPAxis::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<QCPAxis *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->rangeChanged((*reinterpret_cast< const QCPRange(*)>(_a[1]))); break;
        case 1: _t->rangeChanged((*reinterpret_cast< const QCPRange(*)>(_a[1])),(*reinterpret_cast< const QCPRange(*)>(_a[2]))); break;
        case 2: _t->scaleTypeChanged((*reinterpret_cast< QCPAxis::ScaleType(*)>(_a[1]))); break;
        case 3: _t->selectionChanged((*reinterpret_cast< const QCPAxis::SelectableParts(*)>(_a[1]))); break;
        case 4: _t->selectableChanged((*reinterpret_cast< const QCPAxis::SelectableParts(*)>(_a[1]))); break;
        case 5: _t->setScaleType((*reinterpret_cast< QCPAxis::ScaleType(*)>(_a[1]))); break;
        case 6: _t->setRange((*reinterpret_cast< const QCPRange(*)>(_a[1]))); break;
        case 7: _t->setSelectableParts((*reinterpret_cast< const QCPAxis::SelectableParts(*)>(_a[1]))); break;
        case 8: _t->setSelectedParts((*reinterpret_cast< const QCPAxis::SelectableParts(*)>(_a[1]))); break;
        default: ;
        }
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        switch (_id) {
        default: *reinterpret_cast<int*>(_a[0]) = -1; break;
        case 2:
            switch (*reinterpret_cast<int*>(_a[1])) {
            default: *reinterpret_cast<int*>(_a[0]) = -1; break;
            case 0:
                *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< QCPAxis::ScaleType >(); break;
            }
            break;
        case 5:
            switch (*reinterpret_cast<int*>(_a[1])) {
            default: *reinterpret_cast<int*>(_a[0]) = -1; break;
            case 0:
                *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< QCPAxis::ScaleType >(); break;
            }
            break;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (QCPAxis::*)(const QCPRange & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&QCPAxis::rangeChanged)) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (QCPAxis::*)(const QCPRange & , const QCPRange & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&QCPAxis::rangeChanged)) {
                *result = 1;
                return;
            }
        }
        {
            using _t = void (QCPAxis::*)(QCPAxis::ScaleType );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&QCPAxis::scaleTypeChanged)) {
                *result = 2;
                return;
            }
        }
        {
            using _t = void (QCPAxis::*)(const QCPAxis::SelectableParts & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&QCPAxis::selectionChanged)) {
                *result = 3;
                return;
            }
        }
        {
            using _t = void (QCPAxis::*)(const QCPAxis::SelectableParts & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&QCPAxis::selectableChanged)) {
                *result = 4;
                return;
            }
        }
    } else if (_c == QMetaObject::RegisterPropertyMetaType) {
        switch (_id) {
        default: *reinterpret_cast<int*>(_a[0]) = -1; break;
        case 1:
            *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< QCPAxisRect* >(); break;
        case 42:
            *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< QCPGrid* >(); break;
        case 5:
            *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< QSharedPointer<QCPAxisTicker> >(); break;
        case 16:
            *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< QVector<QString> >(); break;
        case 15:
            *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< QVector<double> >(); break;
        }
    }

#ifndef QT_NO_PROPERTIES
    else if (_c == QMetaObject::ReadProperty) {
        auto *_t = static_cast<QCPAxis *>(_o);
        Q_UNUSED(_t)
        void *_v = _a[0];
        switch (_id) {
        case 0: *reinterpret_cast< AxisType*>(_v) = _t->axisType(); break;
        case 1: *reinterpret_cast< QCPAxisRect**>(_v) = _t->axisRect(); break;
        case 2: *reinterpret_cast< ScaleType*>(_v) = _t->scaleType(); break;
        case 3: *reinterpret_cast< QCPRange*>(_v) = _t->range(); break;
        case 4: *reinterpret_cast< bool*>(_v) = _t->rangeReversed(); break;
        case 5: *reinterpret_cast< QSharedPointer<QCPAxisTicker>*>(_v) = _t->ticker(); break;
        case 6: *reinterpret_cast< bool*>(_v) = _t->ticks(); break;
        case 7: *reinterpret_cast< bool*>(_v) = _t->tickLabels(); break;
        case 8: *reinterpret_cast< int*>(_v) = _t->tickLabelPadding(); break;
        case 9: *reinterpret_cast< QFont*>(_v) = _t->tickLabelFont(); break;
        case 10: *reinterpret_cast< QColor*>(_v) = _t->tickLabelColor(); break;
        case 11: *reinterpret_cast< double*>(_v) = _t->tickLabelRotation(); break;
        case 12: *reinterpret_cast< LabelSide*>(_v) = _t->tickLabelSide(); break;
        case 13: *reinterpret_cast< QString*>(_v) = _t->numberFormat(); break;
        case 14: *reinterpret_cast< int*>(_v) = _t->numberPrecision(); break;
        case 15: *reinterpret_cast< QVector<double>*>(_v) = _t->tickVector(); break;
        case 16: *reinterpret_cast< QVector<QString>*>(_v) = _t->tickVectorLabels(); break;
        case 17: *reinterpret_cast< int*>(_v) = _t->tickLengthIn(); break;
        case 18: *reinterpret_cast< int*>(_v) = _t->tickLengthOut(); break;
        case 19: *reinterpret_cast< bool*>(_v) = _t->subTicks(); break;
        case 20: *reinterpret_cast< int*>(_v) = _t->subTickLengthIn(); break;
        case 21: *reinterpret_cast< int*>(_v) = _t->subTickLengthOut(); break;
        case 22: *reinterpret_cast< QPen*>(_v) = _t->basePen(); break;
        case 23: *reinterpret_cast< QPen*>(_v) = _t->tickPen(); break;
        case 24: *reinterpret_cast< QPen*>(_v) = _t->subTickPen(); break;
        case 25: *reinterpret_cast< QFont*>(_v) = _t->labelFont(); break;
        case 26: *reinterpret_cast< QColor*>(_v) = _t->labelColor(); break;
        case 27: *reinterpret_cast< QString*>(_v) = _t->label(); break;
        case 28: *reinterpret_cast< int*>(_v) = _t->labelPadding(); break;
        case 29: *reinterpret_cast< int*>(_v) = _t->padding(); break;
        case 30: *reinterpret_cast< int*>(_v) = _t->offset(); break;
        case 31: *reinterpret_cast<int*>(_v) = QFlag(_t->selectedParts()); break;
        case 32: *reinterpret_cast<int*>(_v) = QFlag(_t->selectableParts()); break;
        case 33: *reinterpret_cast< QFont*>(_v) = _t->selectedTickLabelFont(); break;
        case 34: *reinterpret_cast< QFont*>(_v) = _t->selectedLabelFont(); break;
        case 35: *reinterpret_cast< QColor*>(_v) = _t->selectedTickLabelColor(); break;
        case 36: *reinterpret_cast< QColor*>(_v) = _t->selectedLabelColor(); break;
        case 37: *reinterpret_cast< QPen*>(_v) = _t->selectedBasePen(); break;
        case 38: *reinterpret_cast< QPen*>(_v) = _t->selectedTickPen(); break;
        case 39: *reinterpret_cast< QPen*>(_v) = _t->selectedSubTickPen(); break;
        case 40: *reinterpret_cast< QCPLineEnding*>(_v) = _t->lowerEnding(); break;
        case 41: *reinterpret_cast< QCPLineEnding*>(_v) = _t->upperEnding(); break;
        case 42: *reinterpret_cast< QCPGrid**>(_v) = _t->grid(); break;
        default: break;
        }
    } else if (_c == QMetaObject::WriteProperty) {
        auto *_t = static_cast<QCPAxis *>(_o);
        Q_UNUSED(_t)
        void *_v = _a[0];
        switch (_id) {
        case 2: _t->setScaleType(*reinterpret_cast< ScaleType*>(_v)); break;
        case 3: _t->setRange(*reinterpret_cast< QCPRange*>(_v)); break;
        case 4: _t->setRangeReversed(*reinterpret_cast< bool*>(_v)); break;
        case 5: _t->setTicker(*reinterpret_cast< QSharedPointer<QCPAxisTicker>*>(_v)); break;
        case 6: _t->setTicks(*reinterpret_cast< bool*>(_v)); break;
        case 7: _t->setTickLabels(*reinterpret_cast< bool*>(_v)); break;
        case 8: _t->setTickLabelPadding(*reinterpret_cast< int*>(_v)); break;
        case 9: _t->setTickLabelFont(*reinterpret_cast< QFont*>(_v)); break;
        case 10: _t->setTickLabelColor(*reinterpret_cast< QColor*>(_v)); break;
        case 11: _t->setTickLabelRotation(*reinterpret_cast< double*>(_v)); break;
        case 12: _t->setTickLabelSide(*reinterpret_cast< LabelSide*>(_v)); break;
        case 13: _t->setNumberFormat(*reinterpret_cast< QString*>(_v)); break;
        case 14: _t->setNumberPrecision(*reinterpret_cast< int*>(_v)); break;
        case 17: _t->setTickLengthIn(*reinterpret_cast< int*>(_v)); break;
        case 18: _t->setTickLengthOut(*reinterpret_cast< int*>(_v)); break;
        case 19: _t->setSubTicks(*reinterpret_cast< bool*>(_v)); break;
        case 20: _t->setSubTickLengthIn(*reinterpret_cast< int*>(_v)); break;
        case 21: _t->setSubTickLengthOut(*reinterpret_cast< int*>(_v)); break;
        case 22: _t->setBasePen(*reinterpret_cast< QPen*>(_v)); break;
        case 23: _t->setTickPen(*reinterpret_cast< QPen*>(_v)); break;
        case 24: _t->setSubTickPen(*reinterpret_cast< QPen*>(_v)); break;
        case 25: _t->setLabelFont(*reinterpret_cast< QFont*>(_v)); break;
        case 26: _t->setLabelColor(*reinterpret_cast< QColor*>(_v)); break;
        case 27: _t->setLabel(*reinterpret_cast< QString*>(_v)); break;
        case 28: _t->setLabelPadding(*reinterpret_cast< int*>(_v)); break;
        case 29: _t->setPadding(*reinterpret_cast< int*>(_v)); break;
        case 30: _t->setOffset(*reinterpret_cast< int*>(_v)); break;
        case 31: _t->setSelectedParts(QFlag(*reinterpret_cast<int*>(_v))); break;
        case 32: _t->setSelectableParts(QFlag(*reinterpret_cast<int*>(_v))); break;
        case 33: _t->setSelectedTickLabelFont(*reinterpret_cast< QFont*>(_v)); break;
        case 34: _t->setSelectedLabelFont(*reinterpret_cast< QFont*>(_v)); break;
        case 35: _t->setSelectedTickLabelColor(*reinterpret_cast< QColor*>(_v)); break;
        case 36: _t->setSelectedLabelColor(*reinterpret_cast< QColor*>(_v)); break;
        case 37: _t->setSelectedBasePen(*reinterpret_cast< QPen*>(_v)); break;
        case 38: _t->setSelectedTickPen(*reinterpret_cast< QPen*>(_v)); break;
        case 39: _t->setSelectedSubTickPen(*reinterpret_cast< QPen*>(_v)); break;
        case 40: _t->setLowerEnding(*reinterpret_cast< QCPLineEnding*>(_v)); break;
        case 41: _t->setUpperEnding(*reinterpret_cast< QCPLineEnding*>(_v)); break;
        default: break;
        }
    } else if (_c == QMetaObject::ResetProperty) {
    }
#endif // QT_NO_PROPERTIES
}

QT_INIT_METAOBJECT const QMetaObject QCPAxis::staticMetaObject = { {
    &QCPLayerable::staticMetaObject,
    qt_meta_stringdata_QCPAxis.data,
    qt_meta_data_QCPAxis,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *QCPAxis::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *QCPAxis::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_QCPAxis.stringdata0))
        return static_cast<void*>(this);
    return QCPLayerable::qt_metacast(_clname);
}

int QCPAxis::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QCPLayerable::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 9)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 9;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 9)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 9;
    }
#ifndef QT_NO_PROPERTIES
    else if (_c == QMetaObject::ReadProperty || _c == QMetaObject::WriteProperty
            || _c == QMetaObject::ResetProperty || _c == QMetaObject::RegisterPropertyMetaType) {
        qt_static_metacall(this, _c, _id, _a);
        _id -= 43;
    } else if (_c == QMetaObject::QueryPropertyDesignable) {
        _id -= 43;
    } else if (_c == QMetaObject::QueryPropertyScriptable) {
        _id -= 43;
    } else if (_c == QMetaObject::QueryPropertyStored) {
        _id -= 43;
    } else if (_c == QMetaObject::QueryPropertyEditable) {
        _id -= 43;
    } else if (_c == QMetaObject::QueryPropertyUser) {
        _id -= 43;
    }
#endif // QT_NO_PROPERTIES
    return _id;
}

// SIGNAL 0
void QCPAxis::rangeChanged(const QCPRange & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}

// SIGNAL 1
void QCPAxis::rangeChanged(const QCPRange & _t1, const QCPRange & _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))) };
    QMetaObject::activate(this, &staticMetaObject, 1, _a);
}

// SIGNAL 2
void QCPAxis::scaleTypeChanged(QCPAxis::ScaleType _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 2, _a);
}

// SIGNAL 3
void QCPAxis::selectionChanged(const QCPAxis::SelectableParts & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 3, _a);
}

// SIGNAL 4
void QCPAxis::selectableChanged(const QCPAxis::SelectableParts & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 4, _a);
}
struct qt_meta_stringdata_QCPScatterStyle_t {
    QByteArrayData data[28];
    char stringdata0[285];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_QCPScatterStyle_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_QCPScatterStyle_t qt_meta_stringdata_QCPScatterStyle = {
    {
QT_MOC_LITERAL(0, 0, 15), // "QCPScatterStyle"
QT_MOC_LITERAL(1, 16, 15), // "ScatterProperty"
QT_MOC_LITERAL(2, 32, 6), // "spNone"
QT_MOC_LITERAL(3, 39, 5), // "spPen"
QT_MOC_LITERAL(4, 45, 7), // "spBrush"
QT_MOC_LITERAL(5, 53, 6), // "spSize"
QT_MOC_LITERAL(6, 60, 7), // "spShape"
QT_MOC_LITERAL(7, 68, 5), // "spAll"
QT_MOC_LITERAL(8, 74, 17), // "ScatterProperties"
QT_MOC_LITERAL(9, 92, 12), // "ScatterShape"
QT_MOC_LITERAL(10, 105, 6), // "ssNone"
QT_MOC_LITERAL(11, 112, 5), // "ssDot"
QT_MOC_LITERAL(12, 118, 7), // "ssCross"
QT_MOC_LITERAL(13, 126, 6), // "ssPlus"
QT_MOC_LITERAL(14, 133, 8), // "ssCircle"
QT_MOC_LITERAL(15, 142, 6), // "ssDisc"
QT_MOC_LITERAL(16, 149, 8), // "ssSquare"
QT_MOC_LITERAL(17, 158, 9), // "ssDiamond"
QT_MOC_LITERAL(18, 168, 6), // "ssStar"
QT_MOC_LITERAL(19, 175, 10), // "ssTriangle"
QT_MOC_LITERAL(20, 186, 18), // "ssTriangleInverted"
QT_MOC_LITERAL(21, 205, 13), // "ssCrossSquare"
QT_MOC_LITERAL(22, 219, 12), // "ssPlusSquare"
QT_MOC_LITERAL(23, 232, 13), // "ssCrossCircle"
QT_MOC_LITERAL(24, 246, 12), // "ssPlusCircle"
QT_MOC_LITERAL(25, 259, 7), // "ssPeace"
QT_MOC_LITERAL(26, 267, 8), // "ssPixmap"
QT_MOC_LITERAL(27, 276, 8) // "ssCustom"

    },
    "QCPScatterStyle\0ScatterProperty\0spNone\0"
    "spPen\0spBrush\0spSize\0spShape\0spAll\0"
    "ScatterProperties\0ScatterShape\0ssNone\0"
    "ssDot\0ssCross\0ssPlus\0ssCircle\0ssDisc\0"
    "ssSquare\0ssDiamond\0ssStar\0ssTriangle\0"
    "ssTriangleInverted\0ssCrossSquare\0"
    "ssPlusSquare\0ssCrossCircle\0ssPlusCircle\0"
    "ssPeace\0ssPixmap\0ssCustom"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_QCPScatterStyle[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
       0,    0, // methods
       0,    0, // properties
       3,   14, // enums/sets
       0,    0, // constructors
       4,       // flags
       0,       // signalCount

 // enums: name, alias, flags, count, data
       1,    1, 0x0,    6,   29,
       8,    1, 0x1,    6,   41,
       9,    9, 0x0,   18,   53,

 // enum data: key, value
       2, uint(QCPScatterStyle::spNone),
       3, uint(QCPScatterStyle::spPen),
       4, uint(QCPScatterStyle::spBrush),
       5, uint(QCPScatterStyle::spSize),
       6, uint(QCPScatterStyle::spShape),
       7, uint(QCPScatterStyle::spAll),
       2, uint(QCPScatterStyle::spNone),
       3, uint(QCPScatterStyle::spPen),
       4, uint(QCPScatterStyle::spBrush),
       5, uint(QCPScatterStyle::spSize),
       6, uint(QCPScatterStyle::spShape),
       7, uint(QCPScatterStyle::spAll),
      10, uint(QCPScatterStyle::ssNone),
      11, uint(QCPScatterStyle::ssDot),
      12, uint(QCPScatterStyle::ssCross),
      13, uint(QCPScatterStyle::ssPlus),
      14, uint(QCPScatterStyle::ssCircle),
      15, uint(QCPScatterStyle::ssDisc),
      16, uint(QCPScatterStyle::ssSquare),
      17, uint(QCPScatterStyle::ssDiamond),
      18, uint(QCPScatterStyle::ssStar),
      19, uint(QCPScatterStyle::ssTriangle),
      20, uint(QCPScatterStyle::ssTriangleInverted),
      21, uint(QCPScatterStyle::ssCrossSquare),
      22, uint(QCPScatterStyle::ssPlusSquare),
      23, uint(QCPScatterStyle::ssCrossCircle),
      24, uint(QCPScatterStyle::ssPlusCircle),
      25, uint(QCPScatterStyle::ssPeace),
      26, uint(QCPScatterStyle::ssPixmap),
      27, uint(QCPScatterStyle::ssCustom),

       0        // eod
};

QT_INIT_METAOBJECT const QMetaObject QCPScatterStyle::staticMetaObject = { {
    nullptr,
    qt_meta_stringdata_QCPScatterStyle.data,
    qt_meta_data_QCPScatterStyle,
    nullptr,
    nullptr,
    nullptr
} };

struct qt_meta_stringdata_QCPSelectionDecorator_t {
    QByteArrayData data[1];
    char stringdata0[22];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_QCPSelectionDecorator_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_QCPSelectionDecorator_t qt_meta_stringdata_QCPSelectionDecorator = {
    {
QT_MOC_LITERAL(0, 0, 21) // "QCPSelectionDecorator"

    },
    "QCPSelectionDecorator"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_QCPSelectionDecorator[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
       0,    0, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       4,       // flags
       0,       // signalCount

       0        // eod
};

QT_INIT_METAOBJECT const QMetaObject QCPSelectionDecorator::staticMetaObject = { {
    nullptr,
    qt_meta_stringdata_QCPSelectionDecorator.data,
    qt_meta_data_QCPSelectionDecorator,
    nullptr,
    nullptr,
    nullptr
} };

struct qt_meta_stringdata_QCPAbstractPlottable_t {
    QByteArrayData data[21];
    char stringdata0[270];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_QCPAbstractPlottable_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_QCPAbstractPlottable_t qt_meta_stringdata_QCPAbstractPlottable = {
    {
QT_MOC_LITERAL(0, 0, 20), // "QCPAbstractPlottable"
QT_MOC_LITERAL(1, 21, 16), // "selectionChanged"
QT_MOC_LITERAL(2, 38, 0), // ""
QT_MOC_LITERAL(3, 39, 8), // "selected"
QT_MOC_LITERAL(4, 48, 16), // "QCPDataSelection"
QT_MOC_LITERAL(5, 65, 9), // "selection"
QT_MOC_LITERAL(6, 75, 17), // "selectableChanged"
QT_MOC_LITERAL(7, 93, 18), // "QCP::SelectionType"
QT_MOC_LITERAL(8, 112, 10), // "selectable"
QT_MOC_LITERAL(9, 123, 13), // "setSelectable"
QT_MOC_LITERAL(10, 137, 12), // "setSelection"
QT_MOC_LITERAL(11, 150, 4), // "name"
QT_MOC_LITERAL(12, 155, 15), // "antialiasedFill"
QT_MOC_LITERAL(13, 171, 19), // "antialiasedScatters"
QT_MOC_LITERAL(14, 191, 3), // "pen"
QT_MOC_LITERAL(15, 195, 5), // "brush"
QT_MOC_LITERAL(16, 201, 7), // "keyAxis"
QT_MOC_LITERAL(17, 209, 8), // "QCPAxis*"
QT_MOC_LITERAL(18, 218, 9), // "valueAxis"
QT_MOC_LITERAL(19, 228, 18), // "selectionDecorator"
QT_MOC_LITERAL(20, 247, 22) // "QCPSelectionDecorator*"

    },
    "QCPAbstractPlottable\0selectionChanged\0"
    "\0selected\0QCPDataSelection\0selection\0"
    "selectableChanged\0QCP::SelectionType\0"
    "selectable\0setSelectable\0setSelection\0"
    "name\0antialiasedFill\0antialiasedScatters\0"
    "pen\0brush\0keyAxis\0QCPAxis*\0valueAxis\0"
    "selectionDecorator\0QCPSelectionDecorator*"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_QCPAbstractPlottable[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
       5,   14, // methods
      10,   54, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       3,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    1,   39,    2, 0x06 /* Public */,
       1,    1,   42,    2, 0x06 /* Public */,
       6,    1,   45,    2, 0x06 /* Public */,

 // slots: name, argc, parameters, tag, flags
       9,    1,   48,    2, 0x0a /* Public */,
      10,    1,   51,    2, 0x0a /* Public */,

 // signals: parameters
    QMetaType::Void, QMetaType::Bool,    3,
    QMetaType::Void, 0x80000000 | 4,    5,
    QMetaType::Void, 0x80000000 | 7,    8,

 // slots: parameters
    QMetaType::Void, 0x80000000 | 7,    8,
    QMetaType::Void, 0x80000000 | 4,    5,

 // properties: name, type, flags
      11, QMetaType::QString, 0x00095103,
      12, QMetaType::Bool, 0x00095103,
      13, QMetaType::Bool, 0x00095103,
      14, QMetaType::QPen, 0x00095103,
      15, QMetaType::QBrush, 0x00095103,
      16, 0x80000000 | 17, 0x0009510b,
      18, 0x80000000 | 17, 0x0009510b,
       8, 0x80000000 | 7, 0x0049510b,
       5, 0x80000000 | 4, 0x0049510b,
      19, 0x80000000 | 20, 0x0009510b,

 // properties: notify_signal_id
       0,
       0,
       0,
       0,
       0,
       0,
       0,
       2,
       0,
       0,

       0        // eod
};

void QCPAbstractPlottable::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<QCPAbstractPlottable *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->selectionChanged((*reinterpret_cast< bool(*)>(_a[1]))); break;
        case 1: _t->selectionChanged((*reinterpret_cast< const QCPDataSelection(*)>(_a[1]))); break;
        case 2: _t->selectableChanged((*reinterpret_cast< QCP::SelectionType(*)>(_a[1]))); break;
        case 3: _t->setSelectable((*reinterpret_cast< QCP::SelectionType(*)>(_a[1]))); break;
        case 4: _t->setSelection((*reinterpret_cast< QCPDataSelection(*)>(_a[1]))); break;
        default: ;
        }
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        switch (_id) {
        default: *reinterpret_cast<int*>(_a[0]) = -1; break;
        case 1:
            switch (*reinterpret_cast<int*>(_a[1])) {
            default: *reinterpret_cast<int*>(_a[0]) = -1; break;
            case 0:
                *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< QCPDataSelection >(); break;
            }
            break;
        case 2:
            switch (*reinterpret_cast<int*>(_a[1])) {
            default: *reinterpret_cast<int*>(_a[0]) = -1; break;
            case 0:
                *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< QCP::SelectionType >(); break;
            }
            break;
        case 3:
            switch (*reinterpret_cast<int*>(_a[1])) {
            default: *reinterpret_cast<int*>(_a[0]) = -1; break;
            case 0:
                *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< QCP::SelectionType >(); break;
            }
            break;
        case 4:
            switch (*reinterpret_cast<int*>(_a[1])) {
            default: *reinterpret_cast<int*>(_a[0]) = -1; break;
            case 0:
                *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< QCPDataSelection >(); break;
            }
            break;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (QCPAbstractPlottable::*)(bool );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&QCPAbstractPlottable::selectionChanged)) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (QCPAbstractPlottable::*)(const QCPDataSelection & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&QCPAbstractPlottable::selectionChanged)) {
                *result = 1;
                return;
            }
        }
        {
            using _t = void (QCPAbstractPlottable::*)(QCP::SelectionType );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&QCPAbstractPlottable::selectableChanged)) {
                *result = 2;
                return;
            }
        }
    } else if (_c == QMetaObject::RegisterPropertyMetaType) {
        switch (_id) {
        default: *reinterpret_cast<int*>(_a[0]) = -1; break;
        case 7:
            *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< QCP::SelectionType >(); break;
        case 6:
        case 5:
            *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< QCPAxis* >(); break;
        case 8:
            *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< QCPDataSelection >(); break;
        case 9:
            *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< QCPSelectionDecorator* >(); break;
        }
    }

#ifndef QT_NO_PROPERTIES
    else if (_c == QMetaObject::ReadProperty) {
        auto *_t = static_cast<QCPAbstractPlottable *>(_o);
        Q_UNUSED(_t)
        void *_v = _a[0];
        switch (_id) {
        case 0: *reinterpret_cast< QString*>(_v) = _t->name(); break;
        case 1: *reinterpret_cast< bool*>(_v) = _t->antialiasedFill(); break;
        case 2: *reinterpret_cast< bool*>(_v) = _t->antialiasedScatters(); break;
        case 3: *reinterpret_cast< QPen*>(_v) = _t->pen(); break;
        case 4: *reinterpret_cast< QBrush*>(_v) = _t->brush(); break;
        case 5: *reinterpret_cast< QCPAxis**>(_v) = _t->keyAxis(); break;
        case 6: *reinterpret_cast< QCPAxis**>(_v) = _t->valueAxis(); break;
        case 7: *reinterpret_cast< QCP::SelectionType*>(_v) = _t->selectable(); break;
        case 8: *reinterpret_cast< QCPDataSelection*>(_v) = _t->selection(); break;
        case 9: *reinterpret_cast< QCPSelectionDecorator**>(_v) = _t->selectionDecorator(); break;
        default: break;
        }
    } else if (_c == QMetaObject::WriteProperty) {
        auto *_t = static_cast<QCPAbstractPlottable *>(_o);
        Q_UNUSED(_t)
        void *_v = _a[0];
        switch (_id) {
        case 0: _t->setName(*reinterpret_cast< QString*>(_v)); break;
        case 1: _t->setAntialiasedFill(*reinterpret_cast< bool*>(_v)); break;
        case 2: _t->setAntialiasedScatters(*reinterpret_cast< bool*>(_v)); break;
        case 3: _t->setPen(*reinterpret_cast< QPen*>(_v)); break;
        case 4: _t->setBrush(*reinterpret_cast< QBrush*>(_v)); break;
        case 5: _t->setKeyAxis(*reinterpret_cast< QCPAxis**>(_v)); break;
        case 6: _t->setValueAxis(*reinterpret_cast< QCPAxis**>(_v)); break;
        case 7: _t->setSelectable(*reinterpret_cast< QCP::SelectionType*>(_v)); break;
        case 8: _t->setSelection(*reinterpret_cast< QCPDataSelection*>(_v)); break;
        case 9: _t->setSelectionDecorator(*reinterpret_cast< QCPSelectionDecorator**>(_v)); break;
        default: break;
        }
    } else if (_c == QMetaObject::ResetProperty) {
    }
#endif // QT_NO_PROPERTIES
}

static const QMetaObject * const qt_meta_extradata_QCPAbstractPlottable[] = {
        &QCP::staticMetaObject,
    nullptr
};

QT_INIT_METAOBJECT const QMetaObject QCPAbstractPlottable::staticMetaObject = { {
    &QCPLayerable::staticMetaObject,
    qt_meta_stringdata_QCPAbstractPlottable.data,
    qt_meta_data_QCPAbstractPlottable,
    qt_static_metacall,
    qt_meta_extradata_QCPAbstractPlottable,
    nullptr
} };


const QMetaObject *QCPAbstractPlottable::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *QCPAbstractPlottable::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_QCPAbstractPlottable.stringdata0))
        return static_cast<void*>(this);
    return QCPLayerable::qt_metacast(_clname);
}

int QCPAbstractPlottable::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QCPLayerable::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 5)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 5;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 5)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 5;
    }
#ifndef QT_NO_PROPERTIES
    else if (_c == QMetaObject::ReadProperty || _c == QMetaObject::WriteProperty
            || _c == QMetaObject::ResetProperty || _c == QMetaObject::RegisterPropertyMetaType) {
        qt_static_metacall(this, _c, _id, _a);
        _id -= 10;
    } else if (_c == QMetaObject::QueryPropertyDesignable) {
        _id -= 10;
    } else if (_c == QMetaObject::QueryPropertyScriptable) {
        _id -= 10;
    } else if (_c == QMetaObject::QueryPropertyStored) {
        _id -= 10;
    } else if (_c == QMetaObject::QueryPropertyEditable) {
        _id -= 10;
    } else if (_c == QMetaObject::QueryPropertyUser) {
        _id -= 10;
    }
#endif // QT_NO_PROPERTIES
    return _id;
}

// SIGNAL 0
void QCPAbstractPlottable::selectionChanged(bool _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}

// SIGNAL 1
void QCPAbstractPlottable::selectionChanged(const QCPDataSelection & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 1, _a);
}

// SIGNAL 2
void QCPAbstractPlottable::selectableChanged(QCP::SelectionType _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 2, _a);
}
struct qt_meta_stringdata_QCPItemAnchor_t {
    QByteArrayData data[1];
    char stringdata0[14];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_QCPItemAnchor_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_QCPItemAnchor_t qt_meta_stringdata_QCPItemAnchor = {
    {
QT_MOC_LITERAL(0, 0, 13) // "QCPItemAnchor"

    },
    "QCPItemAnchor"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_QCPItemAnchor[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
       0,    0, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       4,       // flags
       0,       // signalCount

       0        // eod
};

QT_INIT_METAOBJECT const QMetaObject QCPItemAnchor::staticMetaObject = { {
    nullptr,
    qt_meta_stringdata_QCPItemAnchor.data,
    qt_meta_data_QCPItemAnchor,
    nullptr,
    nullptr,
    nullptr
} };

struct qt_meta_stringdata_QCPItemPosition_t {
    QByteArrayData data[6];
    char stringdata0[85];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_QCPItemPosition_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_QCPItemPosition_t qt_meta_stringdata_QCPItemPosition = {
    {
QT_MOC_LITERAL(0, 0, 15), // "QCPItemPosition"
QT_MOC_LITERAL(1, 16, 12), // "PositionType"
QT_MOC_LITERAL(2, 29, 10), // "ptAbsolute"
QT_MOC_LITERAL(3, 40, 15), // "ptViewportRatio"
QT_MOC_LITERAL(4, 56, 15), // "ptAxisRectRatio"
QT_MOC_LITERAL(5, 72, 12) // "ptPlotCoords"

    },
    "QCPItemPosition\0PositionType\0ptAbsolute\0"
    "ptViewportRatio\0ptAxisRectRatio\0"
    "ptPlotCoords"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_QCPItemPosition[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
       0,    0, // methods
       0,    0, // properties
       1,   14, // enums/sets
       0,    0, // constructors
       4,       // flags
       0,       // signalCount

 // enums: name, alias, flags, count, data
       1,    1, 0x0,    4,   19,

 // enum data: key, value
       2, uint(QCPItemPosition::ptAbsolute),
       3, uint(QCPItemPosition::ptViewportRatio),
       4, uint(QCPItemPosition::ptAxisRectRatio),
       5, uint(QCPItemPosition::ptPlotCoords),

       0        // eod
};

QT_INIT_METAOBJECT const QMetaObject QCPItemPosition::staticMetaObject = { {
    &QCPItemAnchor::staticMetaObject,
    qt_meta_stringdata_QCPItemPosition.data,
    qt_meta_data_QCPItemPosition,
    nullptr,
    nullptr,
    nullptr
} };

struct qt_meta_stringdata_QCPAbstractItem_t {
    QByteArrayData data[11];
    char stringdata0[139];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_QCPAbstractItem_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_QCPAbstractItem_t qt_meta_stringdata_QCPAbstractItem = {
    {
QT_MOC_LITERAL(0, 0, 15), // "QCPAbstractItem"
QT_MOC_LITERAL(1, 16, 16), // "selectionChanged"
QT_MOC_LITERAL(2, 33, 0), // ""
QT_MOC_LITERAL(3, 34, 8), // "selected"
QT_MOC_LITERAL(4, 43, 17), // "selectableChanged"
QT_MOC_LITERAL(5, 61, 10), // "selectable"
QT_MOC_LITERAL(6, 72, 13), // "setSelectable"
QT_MOC_LITERAL(7, 86, 11), // "setSelected"
QT_MOC_LITERAL(8, 98, 14), // "clipToAxisRect"
QT_MOC_LITERAL(9, 113, 12), // "clipAxisRect"
QT_MOC_LITERAL(10, 126, 12) // "QCPAxisRect*"

    },
    "QCPAbstractItem\0selectionChanged\0\0"
    "selected\0selectableChanged\0selectable\0"
    "setSelectable\0setSelected\0clipToAxisRect\0"
    "clipAxisRect\0QCPAxisRect*"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_QCPAbstractItem[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
       4,   14, // methods
       4,   46, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       2,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    1,   34,    2, 0x06 /* Public */,
       4,    1,   37,    2, 0x06 /* Public */,

 // slots: name, argc, parameters, tag, flags
       6,    1,   40,    2, 0x0a /* Public */,
       7,    1,   43,    2, 0x0a /* Public */,

 // signals: parameters
    QMetaType::Void, QMetaType::Bool,    3,
    QMetaType::Void, QMetaType::Bool,    5,

 // slots: parameters
    QMetaType::Void, QMetaType::Bool,    5,
    QMetaType::Void, QMetaType::Bool,    3,

 // properties: name, type, flags
       8, QMetaType::Bool, 0x00095103,
       9, 0x80000000 | 10, 0x0009510b,
       5, QMetaType::Bool, 0x00495103,
       3, QMetaType::Bool, 0x00495103,

 // properties: notify_signal_id
       0,
       0,
       1,
       0,

       0        // eod
};

void QCPAbstractItem::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<QCPAbstractItem *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->selectionChanged((*reinterpret_cast< bool(*)>(_a[1]))); break;
        case 1: _t->selectableChanged((*reinterpret_cast< bool(*)>(_a[1]))); break;
        case 2: _t->setSelectable((*reinterpret_cast< bool(*)>(_a[1]))); break;
        case 3: _t->setSelected((*reinterpret_cast< bool(*)>(_a[1]))); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (QCPAbstractItem::*)(bool );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&QCPAbstractItem::selectionChanged)) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (QCPAbstractItem::*)(bool );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&QCPAbstractItem::selectableChanged)) {
                *result = 1;
                return;
            }
        }
    } else if (_c == QMetaObject::RegisterPropertyMetaType) {
        switch (_id) {
        default: *reinterpret_cast<int*>(_a[0]) = -1; break;
        case 1:
            *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< QCPAxisRect* >(); break;
        }
    }

#ifndef QT_NO_PROPERTIES
    else if (_c == QMetaObject::ReadProperty) {
        auto *_t = static_cast<QCPAbstractItem *>(_o);
        Q_UNUSED(_t)
        void *_v = _a[0];
        switch (_id) {
        case 0: *reinterpret_cast< bool*>(_v) = _t->clipToAxisRect(); break;
        case 1: *reinterpret_cast< QCPAxisRect**>(_v) = _t->clipAxisRect(); break;
        case 2: *reinterpret_cast< bool*>(_v) = _t->selectable(); break;
        case 3: *reinterpret_cast< bool*>(_v) = _t->selected(); break;
        default: break;
        }
    } else if (_c == QMetaObject::WriteProperty) {
        auto *_t = static_cast<QCPAbstractItem *>(_o);
        Q_UNUSED(_t)
        void *_v = _a[0];
        switch (_id) {
        case 0: _t->setClipToAxisRect(*reinterpret_cast< bool*>(_v)); break;
        case 1: _t->setClipAxisRect(*reinterpret_cast< QCPAxisRect**>(_v)); break;
        case 2: _t->setSelectable(*reinterpret_cast< bool*>(_v)); break;
        case 3: _t->setSelected(*reinterpret_cast< bool*>(_v)); break;
        default: break;
        }
    } else if (_c == QMetaObject::ResetProperty) {
    }
#endif // QT_NO_PROPERTIES
}

QT_INIT_METAOBJECT const QMetaObject QCPAbstractItem::staticMetaObject = { {
    &QCPLayerable::staticMetaObject,
    qt_meta_stringdata_QCPAbstractItem.data,
    qt_meta_data_QCPAbstractItem,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *QCPAbstractItem::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *QCPAbstractItem::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_QCPAbstractItem.stringdata0))
        return static_cast<void*>(this);
    return QCPLayerable::qt_metacast(_clname);
}

int QCPAbstractItem::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QCPLayerable::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 4)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 4;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 4)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 4;
    }
#ifndef QT_NO_PROPERTIES
    else if (_c == QMetaObject::ReadProperty || _c == QMetaObject::WriteProperty
            || _c == QMetaObject::ResetProperty || _c == QMetaObject::RegisterPropertyMetaType) {
        qt_static_metacall(this, _c, _id, _a);
        _id -= 4;
    } else if (_c == QMetaObject::QueryPropertyDesignable) {
        _id -= 4;
    } else if (_c == QMetaObject::QueryPropertyScriptable) {
        _id -= 4;
    } else if (_c == QMetaObject::QueryPropertyStored) {
        _id -= 4;
    } else if (_c == QMetaObject::QueryPropertyEditable) {
        _id -= 4;
    } else if (_c == QMetaObject::QueryPropertyUser) {
        _id -= 4;
    }
#endif // QT_NO_PROPERTIES
    return _id;
}

// SIGNAL 0
void QCPAbstractItem::selectionChanged(bool _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}

// SIGNAL 1
void QCPAbstractItem::selectableChanged(bool _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 1, _a);
}
struct qt_meta_stringdata_QCustomPlot_t {
    QByteArrayData data[64];
    char stringdata0[914];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_QCustomPlot_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_QCustomPlot_t qt_meta_stringdata_QCustomPlot = {
    {
QT_MOC_LITERAL(0, 0, 11), // "QCustomPlot"
QT_MOC_LITERAL(1, 12, 16), // "mouseDoubleClick"
QT_MOC_LITERAL(2, 29, 0), // ""
QT_MOC_LITERAL(3, 30, 12), // "QMouseEvent*"
QT_MOC_LITERAL(4, 43, 5), // "event"
QT_MOC_LITERAL(5, 49, 10), // "mousePress"
QT_MOC_LITERAL(6, 60, 9), // "mouseMove"
QT_MOC_LITERAL(7, 70, 12), // "mouseRelease"
QT_MOC_LITERAL(8, 83, 10), // "mouseWheel"
QT_MOC_LITERAL(9, 94, 12), // "QWheelEvent*"
QT_MOC_LITERAL(10, 107, 14), // "plottableClick"
QT_MOC_LITERAL(11, 122, 21), // "QCPAbstractPlottable*"
QT_MOC_LITERAL(12, 144, 9), // "plottable"
QT_MOC_LITERAL(13, 154, 9), // "dataIndex"
QT_MOC_LITERAL(14, 164, 20), // "plottableDoubleClick"
QT_MOC_LITERAL(15, 185, 9), // "itemClick"
QT_MOC_LITERAL(16, 195, 16), // "QCPAbstractItem*"
QT_MOC_LITERAL(17, 212, 4), // "item"
QT_MOC_LITERAL(18, 217, 15), // "itemDoubleClick"
QT_MOC_LITERAL(19, 233, 9), // "axisClick"
QT_MOC_LITERAL(20, 243, 8), // "QCPAxis*"
QT_MOC_LITERAL(21, 252, 4), // "axis"
QT_MOC_LITERAL(22, 257, 23), // "QCPAxis::SelectablePart"
QT_MOC_LITERAL(23, 281, 4), // "part"
QT_MOC_LITERAL(24, 286, 15), // "axisDoubleClick"
QT_MOC_LITERAL(25, 302, 11), // "legendClick"
QT_MOC_LITERAL(26, 314, 10), // "QCPLegend*"
QT_MOC_LITERAL(27, 325, 6), // "legend"
QT_MOC_LITERAL(28, 332, 22), // "QCPAbstractLegendItem*"
QT_MOC_LITERAL(29, 355, 17), // "legendDoubleClick"
QT_MOC_LITERAL(30, 373, 22), // "selectionChangedByUser"
QT_MOC_LITERAL(31, 396, 12), // "beforeReplot"
QT_MOC_LITERAL(32, 409, 11), // "afterReplot"
QT_MOC_LITERAL(33, 421, 11), // "rescaleAxes"
QT_MOC_LITERAL(34, 433, 21), // "onlyVisiblePlottables"
QT_MOC_LITERAL(35, 455, 11), // "deselectAll"
QT_MOC_LITERAL(36, 467, 6), // "replot"
QT_MOC_LITERAL(37, 474, 28), // "QCustomPlot::RefreshPriority"
QT_MOC_LITERAL(38, 503, 15), // "refreshPriority"
QT_MOC_LITERAL(39, 519, 20), // "processRectSelection"
QT_MOC_LITERAL(40, 540, 4), // "rect"
QT_MOC_LITERAL(41, 545, 15), // "processRectZoom"
QT_MOC_LITERAL(42, 561, 21), // "processPointSelection"
QT_MOC_LITERAL(43, 583, 8), // "viewport"
QT_MOC_LITERAL(44, 592, 10), // "background"
QT_MOC_LITERAL(45, 603, 16), // "backgroundScaled"
QT_MOC_LITERAL(46, 620, 20), // "backgroundScaledMode"
QT_MOC_LITERAL(47, 641, 19), // "Qt::AspectRatioMode"
QT_MOC_LITERAL(48, 661, 10), // "plotLayout"
QT_MOC_LITERAL(49, 672, 14), // "QCPLayoutGrid*"
QT_MOC_LITERAL(50, 687, 24), // "autoAddPlottableToLegend"
QT_MOC_LITERAL(51, 712, 18), // "selectionTolerance"
QT_MOC_LITERAL(52, 731, 20), // "noAntialiasingOnDrag"
QT_MOC_LITERAL(53, 752, 19), // "multiSelectModifier"
QT_MOC_LITERAL(54, 772, 20), // "Qt::KeyboardModifier"
QT_MOC_LITERAL(55, 793, 6), // "openGl"
QT_MOC_LITERAL(56, 800, 15), // "LayerInsertMode"
QT_MOC_LITERAL(57, 816, 8), // "limBelow"
QT_MOC_LITERAL(58, 825, 8), // "limAbove"
QT_MOC_LITERAL(59, 834, 15), // "RefreshPriority"
QT_MOC_LITERAL(60, 850, 18), // "rpImmediateRefresh"
QT_MOC_LITERAL(61, 869, 15), // "rpQueuedRefresh"
QT_MOC_LITERAL(62, 885, 13), // "rpRefreshHint"
QT_MOC_LITERAL(63, 899, 14) // "rpQueuedReplot"

    },
    "QCustomPlot\0mouseDoubleClick\0\0"
    "QMouseEvent*\0event\0mousePress\0mouseMove\0"
    "mouseRelease\0mouseWheel\0QWheelEvent*\0"
    "plottableClick\0QCPAbstractPlottable*\0"
    "plottable\0dataIndex\0plottableDoubleClick\0"
    "itemClick\0QCPAbstractItem*\0item\0"
    "itemDoubleClick\0axisClick\0QCPAxis*\0"
    "axis\0QCPAxis::SelectablePart\0part\0"
    "axisDoubleClick\0legendClick\0QCPLegend*\0"
    "legend\0QCPAbstractLegendItem*\0"
    "legendDoubleClick\0selectionChangedByUser\0"
    "beforeReplot\0afterReplot\0rescaleAxes\0"
    "onlyVisiblePlottables\0deselectAll\0"
    "replot\0QCustomPlot::RefreshPriority\0"
    "refreshPriority\0processRectSelection\0"
    "rect\0processRectZoom\0processPointSelection\0"
    "viewport\0background\0backgroundScaled\0"
    "backgroundScaledMode\0Qt::AspectRatioMode\0"
    "plotLayout\0QCPLayoutGrid*\0"
    "autoAddPlottableToLegend\0selectionTolerance\0"
    "noAntialiasingOnDrag\0multiSelectModifier\0"
    "Qt::KeyboardModifier\0openGl\0LayerInsertMode\0"
    "limBelow\0limAbove\0RefreshPriority\0"
    "rpImmediateRefresh\0rpQueuedRefresh\0"
    "rpRefreshHint\0rpQueuedReplot"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_QCustomPlot[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
      24,   14, // methods
      10,  226, // properties
       2,  256, // enums/sets
       0,    0, // constructors
       0,       // flags
      16,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    1,  134,    2, 0x06 /* Public */,
       5,    1,  137,    2, 0x06 /* Public */,
       6,    1,  140,    2, 0x06 /* Public */,
       7,    1,  143,    2, 0x06 /* Public */,
       8,    1,  146,    2, 0x06 /* Public */,
      10,    3,  149,    2, 0x06 /* Public */,
      14,    3,  156,    2, 0x06 /* Public */,
      15,    2,  163,    2, 0x06 /* Public */,
      18,    2,  168,    2, 0x06 /* Public */,
      19,    3,  173,    2, 0x06 /* Public */,
      24,    3,  180,    2, 0x06 /* Public */,
      25,    3,  187,    2, 0x06 /* Public */,
      29,    3,  194,    2, 0x06 /* Public */,
      30,    0,  201,    2, 0x06 /* Public */,
      31,    0,  202,    2, 0x06 /* Public */,
      32,    0,  203,    2, 0x06 /* Public */,

 // slots: name, argc, parameters, tag, flags
      33,    1,  204,    2, 0x0a /* Public */,
      33,    0,  207,    2, 0x2a /* Public | MethodCloned */,
      35,    0,  208,    2, 0x0a /* Public */,
      36,    1,  209,    2, 0x0a /* Public */,
      36,    0,  212,    2, 0x2a /* Public | MethodCloned */,
      39,    2,  213,    2, 0x09 /* Protected */,
      41,    2,  218,    2, 0x09 /* Protected */,
      42,    1,  223,    2, 0x09 /* Protected */,

 // signals: parameters
    QMetaType::Void, 0x80000000 | 3,    4,
    QMetaType::Void, 0x80000000 | 3,    4,
    QMetaType::Void, 0x80000000 | 3,    4,
    QMetaType::Void, 0x80000000 | 3,    4,
    QMetaType::Void, 0x80000000 | 9,    4,
    QMetaType::Void, 0x80000000 | 11, QMetaType::Int, 0x80000000 | 3,   12,   13,    4,
    QMetaType::Void, 0x80000000 | 11, QMetaType::Int, 0x80000000 | 3,   12,   13,    4,
    QMetaType::Void, 0x80000000 | 16, 0x80000000 | 3,   17,    4,
    QMetaType::Void, 0x80000000 | 16, 0x80000000 | 3,   17,    4,
    QMetaType::Void, 0x80000000 | 20, 0x80000000 | 22, 0x80000000 | 3,   21,   23,    4,
    QMetaType::Void, 0x80000000 | 20, 0x80000000 | 22, 0x80000000 | 3,   21,   23,    4,
    QMetaType::Void, 0x80000000 | 26, 0x80000000 | 28, 0x80000000 | 3,   27,   17,    4,
    QMetaType::Void, 0x80000000 | 26, 0x80000000 | 28, 0x80000000 | 3,   27,   17,    4,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,

 // slots: parameters
    QMetaType::Void, QMetaType::Bool,   34,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, 0x80000000 | 37,   38,
    QMetaType::Void,
    QMetaType::Void, QMetaType::QRect, 0x80000000 | 3,   40,    4,
    QMetaType::Void, QMetaType::QRect, 0x80000000 | 3,   40,    4,
    QMetaType::Void, 0x80000000 | 3,    4,

 // properties: name, type, flags
      43, QMetaType::QRect, 0x00095103,
      44, QMetaType::QPixmap, 0x00095103,
      45, QMetaType::Bool, 0x00095103,
      46, 0x80000000 | 47, 0x0009510b,
      48, 0x80000000 | 49, 0x00095009,
      50, QMetaType::Bool, 0x00095103,
      51, QMetaType::Int, 0x00095103,
      52, QMetaType::Bool, 0x00095103,
      53, 0x80000000 | 54, 0x0009510b,
      55, QMetaType::Bool, 0x00095103,

 // enums: name, alias, flags, count, data
      56,   56, 0x0,    2,  266,
      59,   59, 0x0,    4,  270,

 // enum data: key, value
      57, uint(QCustomPlot::limBelow),
      58, uint(QCustomPlot::limAbove),
      60, uint(QCustomPlot::rpImmediateRefresh),
      61, uint(QCustomPlot::rpQueuedRefresh),
      62, uint(QCustomPlot::rpRefreshHint),
      63, uint(QCustomPlot::rpQueuedReplot),

       0        // eod
};

void QCustomPlot::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<QCustomPlot *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->mouseDoubleClick((*reinterpret_cast< QMouseEvent*(*)>(_a[1]))); break;
        case 1: _t->mousePress((*reinterpret_cast< QMouseEvent*(*)>(_a[1]))); break;
        case 2: _t->mouseMove((*reinterpret_cast< QMouseEvent*(*)>(_a[1]))); break;
        case 3: _t->mouseRelease((*reinterpret_cast< QMouseEvent*(*)>(_a[1]))); break;
        case 4: _t->mouseWheel((*reinterpret_cast< QWheelEvent*(*)>(_a[1]))); break;
        case 5: _t->plottableClick((*reinterpret_cast< QCPAbstractPlottable*(*)>(_a[1])),(*reinterpret_cast< int(*)>(_a[2])),(*reinterpret_cast< QMouseEvent*(*)>(_a[3]))); break;
        case 6: _t->plottableDoubleClick((*reinterpret_cast< QCPAbstractPlottable*(*)>(_a[1])),(*reinterpret_cast< int(*)>(_a[2])),(*reinterpret_cast< QMouseEvent*(*)>(_a[3]))); break;
        case 7: _t->itemClick((*reinterpret_cast< QCPAbstractItem*(*)>(_a[1])),(*reinterpret_cast< QMouseEvent*(*)>(_a[2]))); break;
        case 8: _t->itemDoubleClick((*reinterpret_cast< QCPAbstractItem*(*)>(_a[1])),(*reinterpret_cast< QMouseEvent*(*)>(_a[2]))); break;
        case 9: _t->axisClick((*reinterpret_cast< QCPAxis*(*)>(_a[1])),(*reinterpret_cast< QCPAxis::SelectablePart(*)>(_a[2])),(*reinterpret_cast< QMouseEvent*(*)>(_a[3]))); break;
        case 10: _t->axisDoubleClick((*reinterpret_cast< QCPAxis*(*)>(_a[1])),(*reinterpret_cast< QCPAxis::SelectablePart(*)>(_a[2])),(*reinterpret_cast< QMouseEvent*(*)>(_a[3]))); break;
        case 11: _t->legendClick((*reinterpret_cast< QCPLegend*(*)>(_a[1])),(*reinterpret_cast< QCPAbstractLegendItem*(*)>(_a[2])),(*reinterpret_cast< QMouseEvent*(*)>(_a[3]))); break;
        case 12: _t->legendDoubleClick((*reinterpret_cast< QCPLegend*(*)>(_a[1])),(*reinterpret_cast< QCPAbstractLegendItem*(*)>(_a[2])),(*reinterpret_cast< QMouseEvent*(*)>(_a[3]))); break;
        case 13: _t->selectionChangedByUser(); break;
        case 14: _t->beforeReplot(); break;
        case 15: _t->afterReplot(); break;
        case 16: _t->rescaleAxes((*reinterpret_cast< bool(*)>(_a[1]))); break;
        case 17: _t->rescaleAxes(); break;
        case 18: _t->deselectAll(); break;
        case 19: _t->replot((*reinterpret_cast< QCustomPlot::RefreshPriority(*)>(_a[1]))); break;
        case 20: _t->replot(); break;
        case 21: _t->processRectSelection((*reinterpret_cast< QRect(*)>(_a[1])),(*reinterpret_cast< QMouseEvent*(*)>(_a[2]))); break;
        case 22: _t->processRectZoom((*reinterpret_cast< QRect(*)>(_a[1])),(*reinterpret_cast< QMouseEvent*(*)>(_a[2]))); break;
        case 23: _t->processPointSelection((*reinterpret_cast< QMouseEvent*(*)>(_a[1]))); break;
        default: ;
        }
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        switch (_id) {
        default: *reinterpret_cast<int*>(_a[0]) = -1; break;
        case 5:
            switch (*reinterpret_cast<int*>(_a[1])) {
            default: *reinterpret_cast<int*>(_a[0]) = -1; break;
            case 0:
                *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< QCPAbstractPlottable* >(); break;
            }
            break;
        case 6:
            switch (*reinterpret_cast<int*>(_a[1])) {
            default: *reinterpret_cast<int*>(_a[0]) = -1; break;
            case 0:
                *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< QCPAbstractPlottable* >(); break;
            }
            break;
        case 7:
            switch (*reinterpret_cast<int*>(_a[1])) {
            default: *reinterpret_cast<int*>(_a[0]) = -1; break;
            case 0:
                *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< QCPAbstractItem* >(); break;
            }
            break;
        case 8:
            switch (*reinterpret_cast<int*>(_a[1])) {
            default: *reinterpret_cast<int*>(_a[0]) = -1; break;
            case 0:
                *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< QCPAbstractItem* >(); break;
            }
            break;
        case 9:
            switch (*reinterpret_cast<int*>(_a[1])) {
            default: *reinterpret_cast<int*>(_a[0]) = -1; break;
            case 0:
                *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< QCPAxis* >(); break;
            case 1:
                *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< QCPAxis::SelectablePart >(); break;
            }
            break;
        case 10:
            switch (*reinterpret_cast<int*>(_a[1])) {
            default: *reinterpret_cast<int*>(_a[0]) = -1; break;
            case 0:
                *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< QCPAxis* >(); break;
            case 1:
                *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< QCPAxis::SelectablePart >(); break;
            }
            break;
        case 11:
            switch (*reinterpret_cast<int*>(_a[1])) {
            default: *reinterpret_cast<int*>(_a[0]) = -1; break;
            case 1:
                *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< QCPAbstractLegendItem* >(); break;
            case 0:
                *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< QCPLegend* >(); break;
            }
            break;
        case 12:
            switch (*reinterpret_cast<int*>(_a[1])) {
            default: *reinterpret_cast<int*>(_a[0]) = -1; break;
            case 1:
                *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< QCPAbstractLegendItem* >(); break;
            case 0:
                *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< QCPLegend* >(); break;
            }
            break;
        case 19:
            switch (*reinterpret_cast<int*>(_a[1])) {
            default: *reinterpret_cast<int*>(_a[0]) = -1; break;
            case 0:
                *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< QCustomPlot::RefreshPriority >(); break;
            }
            break;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (QCustomPlot::*)(QMouseEvent * );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&QCustomPlot::mouseDoubleClick)) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (QCustomPlot::*)(QMouseEvent * );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&QCustomPlot::mousePress)) {
                *result = 1;
                return;
            }
        }
        {
            using _t = void (QCustomPlot::*)(QMouseEvent * );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&QCustomPlot::mouseMove)) {
                *result = 2;
                return;
            }
        }
        {
            using _t = void (QCustomPlot::*)(QMouseEvent * );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&QCustomPlot::mouseRelease)) {
                *result = 3;
                return;
            }
        }
        {
            using _t = void (QCustomPlot::*)(QWheelEvent * );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&QCustomPlot::mouseWheel)) {
                *result = 4;
                return;
            }
        }
        {
            using _t = void (QCustomPlot::*)(QCPAbstractPlottable * , int , QMouseEvent * );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&QCustomPlot::plottableClick)) {
                *result = 5;
                return;
            }
        }
        {
            using _t = void (QCustomPlot::*)(QCPAbstractPlottable * , int , QMouseEvent * );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&QCustomPlot::plottableDoubleClick)) {
                *result = 6;
                return;
            }
        }
        {
            using _t = void (QCustomPlot::*)(QCPAbstractItem * , QMouseEvent * );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&QCustomPlot::itemClick)) {
                *result = 7;
                return;
            }
        }
        {
            using _t = void (QCustomPlot::*)(QCPAbstractItem * , QMouseEvent * );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&QCustomPlot::itemDoubleClick)) {
                *result = 8;
                return;
            }
        }
        {
            using _t = void (QCustomPlot::*)(QCPAxis * , QCPAxis::SelectablePart , QMouseEvent * );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&QCustomPlot::axisClick)) {
                *result = 9;
                return;
            }
        }
        {
            using _t = void (QCustomPlot::*)(QCPAxis * , QCPAxis::SelectablePart , QMouseEvent * );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&QCustomPlot::axisDoubleClick)) {
                *result = 10;
                return;
            }
        }
        {
            using _t = void (QCustomPlot::*)(QCPLegend * , QCPAbstractLegendItem * , QMouseEvent * );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&QCustomPlot::legendClick)) {
                *result = 11;
                return;
            }
        }
        {
            using _t = void (QCustomPlot::*)(QCPLegend * , QCPAbstractLegendItem * , QMouseEvent * );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&QCustomPlot::legendDoubleClick)) {
                *result = 12;
                return;
            }
        }
        {
            using _t = void (QCustomPlot::*)();
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&QCustomPlot::selectionChangedByUser)) {
                *result = 13;
                return;
            }
        }
        {
            using _t = void (QCustomPlot::*)();
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&QCustomPlot::beforeReplot)) {
                *result = 14;
                return;
            }
        }
        {
            using _t = void (QCustomPlot::*)();
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&QCustomPlot::afterReplot)) {
                *result = 15;
                return;
            }
        }
    } else if (_c == QMetaObject::RegisterPropertyMetaType) {
        switch (_id) {
        default: *reinterpret_cast<int*>(_a[0]) = -1; break;
        case 4:
            *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< QCPLayoutGrid* >(); break;
        }
    }

#ifndef QT_NO_PROPERTIES
    else if (_c == QMetaObject::ReadProperty) {
        auto *_t = static_cast<QCustomPlot *>(_o);
        Q_UNUSED(_t)
        void *_v = _a[0];
        switch (_id) {
        case 0: *reinterpret_cast< QRect*>(_v) = _t->viewport(); break;
        case 1: *reinterpret_cast< QPixmap*>(_v) = _t->background(); break;
        case 2: *reinterpret_cast< bool*>(_v) = _t->backgroundScaled(); break;
        case 3: *reinterpret_cast< Qt::AspectRatioMode*>(_v) = _t->backgroundScaledMode(); break;
        case 4: *reinterpret_cast< QCPLayoutGrid**>(_v) = _t->plotLayout(); break;
        case 5: *reinterpret_cast< bool*>(_v) = _t->autoAddPlottableToLegend(); break;
        case 6: *reinterpret_cast< int*>(_v) = _t->selectionTolerance(); break;
        case 7: *reinterpret_cast< bool*>(_v) = _t->noAntialiasingOnDrag(); break;
        case 8: *reinterpret_cast< Qt::KeyboardModifier*>(_v) = _t->multiSelectModifier(); break;
        case 9: *reinterpret_cast< bool*>(_v) = _t->openGl(); break;
        default: break;
        }
    } else if (_c == QMetaObject::WriteProperty) {
        auto *_t = static_cast<QCustomPlot *>(_o);
        Q_UNUSED(_t)
        void *_v = _a[0];
        switch (_id) {
        case 0: _t->setViewport(*reinterpret_cast< QRect*>(_v)); break;
        case 1: _t->setBackground(*reinterpret_cast< QPixmap*>(_v)); break;
        case 2: _t->setBackgroundScaled(*reinterpret_cast< bool*>(_v)); break;
        case 3: _t->setBackgroundScaledMode(*reinterpret_cast< Qt::AspectRatioMode*>(_v)); break;
        case 5: _t->setAutoAddPlottableToLegend(*reinterpret_cast< bool*>(_v)); break;
        case 6: _t->setSelectionTolerance(*reinterpret_cast< int*>(_v)); break;
        case 7: _t->setNoAntialiasingOnDrag(*reinterpret_cast< bool*>(_v)); break;
        case 8: _t->setMultiSelectModifier(*reinterpret_cast< Qt::KeyboardModifier*>(_v)); break;
        case 9: _t->setOpenGl(*reinterpret_cast< bool*>(_v)); break;
        default: break;
        }
    } else if (_c == QMetaObject::ResetProperty) {
    }
#endif // QT_NO_PROPERTIES
}

QT_INIT_METAOBJECT const QMetaObject QCustomPlot::staticMetaObject = { {
    &QWidget::staticMetaObject,
    qt_meta_stringdata_QCustomPlot.data,
    qt_meta_data_QCustomPlot,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *QCustomPlot::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *QCustomPlot::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_QCustomPlot.stringdata0))
        return static_cast<void*>(this);
    return QWidget::qt_metacast(_clname);
}

int QCustomPlot::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QWidget::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 24)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 24;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 24)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 24;
    }
#ifndef QT_NO_PROPERTIES
    else if (_c == QMetaObject::ReadProperty || _c == QMetaObject::WriteProperty
            || _c == QMetaObject::ResetProperty || _c == QMetaObject::RegisterPropertyMetaType) {
        qt_static_metacall(this, _c, _id, _a);
        _id -= 10;
    } else if (_c == QMetaObject::QueryPropertyDesignable) {
        _id -= 10;
    } else if (_c == QMetaObject::QueryPropertyScriptable) {
        _id -= 10;
    } else if (_c == QMetaObject::QueryPropertyStored) {
        _id -= 10;
    } else if (_c == QMetaObject::QueryPropertyEditable) {
        _id -= 10;
    } else if (_c == QMetaObject::QueryPropertyUser) {
        _id -= 10;
    }
#endif // QT_NO_PROPERTIES
    return _id;
}

// SIGNAL 0
void QCustomPlot::mouseDoubleClick(QMouseEvent * _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}

// SIGNAL 1
void QCustomPlot::mousePress(QMouseEvent * _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 1, _a);
}

// SIGNAL 2
void QCustomPlot::mouseMove(QMouseEvent * _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 2, _a);
}

// SIGNAL 3
void QCustomPlot::mouseRelease(QMouseEvent * _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 3, _a);
}

// SIGNAL 4
void QCustomPlot::mouseWheel(QWheelEvent * _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 4, _a);
}

// SIGNAL 5
void QCustomPlot::plottableClick(QCPAbstractPlottable * _t1, int _t2, QMouseEvent * _t3)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t3))) };
    QMetaObject::activate(this, &staticMetaObject, 5, _a);
}

// SIGNAL 6
void QCustomPlot::plottableDoubleClick(QCPAbstractPlottable * _t1, int _t2, QMouseEvent * _t3)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t3))) };
    QMetaObject::activate(this, &staticMetaObject, 6, _a);
}

// SIGNAL 7
void QCustomPlot::itemClick(QCPAbstractItem * _t1, QMouseEvent * _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))) };
    QMetaObject::activate(this, &staticMetaObject, 7, _a);
}

// SIGNAL 8
void QCustomPlot::itemDoubleClick(QCPAbstractItem * _t1, QMouseEvent * _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))) };
    QMetaObject::activate(this, &staticMetaObject, 8, _a);
}

// SIGNAL 9
void QCustomPlot::axisClick(QCPAxis * _t1, QCPAxis::SelectablePart _t2, QMouseEvent * _t3)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t3))) };
    QMetaObject::activate(this, &staticMetaObject, 9, _a);
}

// SIGNAL 10
void QCustomPlot::axisDoubleClick(QCPAxis * _t1, QCPAxis::SelectablePart _t2, QMouseEvent * _t3)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t3))) };
    QMetaObject::activate(this, &staticMetaObject, 10, _a);
}

// SIGNAL 11
void QCustomPlot::legendClick(QCPLegend * _t1, QCPAbstractLegendItem * _t2, QMouseEvent * _t3)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t3))) };
    QMetaObject::activate(this, &staticMetaObject, 11, _a);
}

// SIGNAL 12
void QCustomPlot::legendDoubleClick(QCPLegend * _t1, QCPAbstractLegendItem * _t2, QMouseEvent * _t3)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t3))) };
    QMetaObject::activate(this, &staticMetaObject, 12, _a);
}

// SIGNAL 13
void QCustomPlot::selectionChangedByUser()
{
    QMetaObject::activate(this, &staticMetaObject, 13, nullptr);
}

// SIGNAL 14
void QCustomPlot::beforeReplot()
{
    QMetaObject::activate(this, &staticMetaObject, 14, nullptr);
}

// SIGNAL 15
void QCustomPlot::afterReplot()
{
    QMetaObject::activate(this, &staticMetaObject, 15, nullptr);
}
struct qt_meta_stringdata_QCPColorGradient_t {
    QByteArrayData data[17];
    char stringdata0[164];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_QCPColorGradient_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_QCPColorGradient_t qt_meta_stringdata_QCPColorGradient = {
    {
QT_MOC_LITERAL(0, 0, 16), // "QCPColorGradient"
QT_MOC_LITERAL(1, 17, 18), // "ColorInterpolation"
QT_MOC_LITERAL(2, 36, 5), // "ciRGB"
QT_MOC_LITERAL(3, 42, 5), // "ciHSV"
QT_MOC_LITERAL(4, 48, 14), // "GradientPreset"
QT_MOC_LITERAL(5, 63, 11), // "gpGrayscale"
QT_MOC_LITERAL(6, 75, 5), // "gpHot"
QT_MOC_LITERAL(7, 81, 6), // "gpCold"
QT_MOC_LITERAL(8, 88, 7), // "gpNight"
QT_MOC_LITERAL(9, 96, 7), // "gpCandy"
QT_MOC_LITERAL(10, 104, 11), // "gpGeography"
QT_MOC_LITERAL(11, 116, 5), // "gpIon"
QT_MOC_LITERAL(12, 122, 9), // "gpThermal"
QT_MOC_LITERAL(13, 132, 7), // "gpPolar"
QT_MOC_LITERAL(14, 140, 10), // "gpSpectrum"
QT_MOC_LITERAL(15, 151, 5), // "gpJet"
QT_MOC_LITERAL(16, 157, 6) // "gpHues"

    },
    "QCPColorGradient\0ColorInterpolation\0"
    "ciRGB\0ciHSV\0GradientPreset\0gpGrayscale\0"
    "gpHot\0gpCold\0gpNight\0gpCandy\0gpGeography\0"
    "gpIon\0gpThermal\0gpPolar\0gpSpectrum\0"
    "gpJet\0gpHues"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_QCPColorGradient[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
       0,    0, // methods
       0,    0, // properties
       2,   14, // enums/sets
       0,    0, // constructors
       4,       // flags
       0,       // signalCount

 // enums: name, alias, flags, count, data
       1,    1, 0x0,    2,   24,
       4,    4, 0x0,   12,   28,

 // enum data: key, value
       2, uint(QCPColorGradient::ciRGB),
       3, uint(QCPColorGradient::ciHSV),
       5, uint(QCPColorGradient::gpGrayscale),
       6, uint(QCPColorGradient::gpHot),
       7, uint(QCPColorGradient::gpCold),
       8, uint(QCPColorGradient::gpNight),
       9, uint(QCPColorGradient::gpCandy),
      10, uint(QCPColorGradient::gpGeography),
      11, uint(QCPColorGradient::gpIon),
      12, uint(QCPColorGradient::gpThermal),
      13, uint(QCPColorGradient::gpPolar),
      14, uint(QCPColorGradient::gpSpectrum),
      15, uint(QCPColorGradient::gpJet),
      16, uint(QCPColorGradient::gpHues),

       0        // eod
};

QT_INIT_METAOBJECT const QMetaObject QCPColorGradient::staticMetaObject = { {
    nullptr,
    qt_meta_stringdata_QCPColorGradient.data,
    qt_meta_data_QCPColorGradient,
    nullptr,
    nullptr,
    nullptr
} };

struct qt_meta_stringdata_QCPSelectionDecoratorBracket_t {
    QByteArrayData data[7];
    char stringdata0[101];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_QCPSelectionDecoratorBracket_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_QCPSelectionDecoratorBracket_t qt_meta_stringdata_QCPSelectionDecoratorBracket = {
    {
QT_MOC_LITERAL(0, 0, 28), // "QCPSelectionDecoratorBracket"
QT_MOC_LITERAL(1, 29, 12), // "BracketStyle"
QT_MOC_LITERAL(2, 42, 15), // "bsSquareBracket"
QT_MOC_LITERAL(3, 58, 13), // "bsHalfEllipse"
QT_MOC_LITERAL(4, 72, 9), // "bsEllipse"
QT_MOC_LITERAL(5, 82, 6), // "bsPlus"
QT_MOC_LITERAL(6, 89, 11) // "bsUserStyle"

    },
    "QCPSelectionDecoratorBracket\0BracketStyle\0"
    "bsSquareBracket\0bsHalfEllipse\0bsEllipse\0"
    "bsPlus\0bsUserStyle"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_QCPSelectionDecoratorBracket[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
       0,    0, // methods
       0,    0, // properties
       1,   14, // enums/sets
       0,    0, // constructors
       4,       // flags
       0,       // signalCount

 // enums: name, alias, flags, count, data
       1,    1, 0x0,    5,   19,

 // enum data: key, value
       2, uint(QCPSelectionDecoratorBracket::bsSquareBracket),
       3, uint(QCPSelectionDecoratorBracket::bsHalfEllipse),
       4, uint(QCPSelectionDecoratorBracket::bsEllipse),
       5, uint(QCPSelectionDecoratorBracket::bsPlus),
       6, uint(QCPSelectionDecoratorBracket::bsUserStyle),

       0        // eod
};

QT_INIT_METAOBJECT const QMetaObject QCPSelectionDecoratorBracket::staticMetaObject = { {
    &QCPSelectionDecorator::staticMetaObject,
    qt_meta_stringdata_QCPSelectionDecoratorBracket.data,
    qt_meta_data_QCPSelectionDecoratorBracket,
    nullptr,
    nullptr,
    nullptr
} };

struct qt_meta_stringdata_QCPAxisRect_t {
    QByteArrayData data[8];
    char stringdata0[118];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_QCPAxisRect_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_QCPAxisRect_t qt_meta_stringdata_QCPAxisRect = {
    {
QT_MOC_LITERAL(0, 0, 11), // "QCPAxisRect"
QT_MOC_LITERAL(1, 12, 10), // "background"
QT_MOC_LITERAL(2, 23, 16), // "backgroundScaled"
QT_MOC_LITERAL(3, 40, 20), // "backgroundScaledMode"
QT_MOC_LITERAL(4, 61, 19), // "Qt::AspectRatioMode"
QT_MOC_LITERAL(5, 81, 9), // "rangeDrag"
QT_MOC_LITERAL(6, 91, 16), // "Qt::Orientations"
QT_MOC_LITERAL(7, 108, 9) // "rangeZoom"

    },
    "QCPAxisRect\0background\0backgroundScaled\0"
    "backgroundScaledMode\0Qt::AspectRatioMode\0"
    "rangeDrag\0Qt::Orientations\0rangeZoom"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_QCPAxisRect[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
       0,    0, // methods
       5,   14, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

 // properties: name, type, flags
       1, QMetaType::QPixmap, 0x00095103,
       2, QMetaType::Bool, 0x00095103,
       3, 0x80000000 | 4, 0x0009510b,
       5, 0x80000000 | 6, 0x0009510b,
       7, 0x80000000 | 6, 0x0009510b,

       0        // eod
};

void QCPAxisRect::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{

#ifndef QT_NO_PROPERTIES
    if (_c == QMetaObject::ReadProperty) {
        auto *_t = static_cast<QCPAxisRect *>(_o);
        Q_UNUSED(_t)
        void *_v = _a[0];
        switch (_id) {
        case 0: *reinterpret_cast< QPixmap*>(_v) = _t->background(); break;
        case 1: *reinterpret_cast< bool*>(_v) = _t->backgroundScaled(); break;
        case 2: *reinterpret_cast< Qt::AspectRatioMode*>(_v) = _t->backgroundScaledMode(); break;
        case 3: *reinterpret_cast< Qt::Orientations*>(_v) = _t->rangeDrag(); break;
        case 4: *reinterpret_cast< Qt::Orientations*>(_v) = _t->rangeZoom(); break;
        default: break;
        }
    } else if (_c == QMetaObject::WriteProperty) {
        auto *_t = static_cast<QCPAxisRect *>(_o);
        Q_UNUSED(_t)
        void *_v = _a[0];
        switch (_id) {
        case 0: _t->setBackground(*reinterpret_cast< QPixmap*>(_v)); break;
        case 1: _t->setBackgroundScaled(*reinterpret_cast< bool*>(_v)); break;
        case 2: _t->setBackgroundScaledMode(*reinterpret_cast< Qt::AspectRatioMode*>(_v)); break;
        case 3: _t->setRangeDrag(*reinterpret_cast< Qt::Orientations*>(_v)); break;
        case 4: _t->setRangeZoom(*reinterpret_cast< Qt::Orientations*>(_v)); break;
        default: break;
        }
    } else if (_c == QMetaObject::ResetProperty) {
    }
#endif // QT_NO_PROPERTIES
    Q_UNUSED(_o);
    Q_UNUSED(_id);
    Q_UNUSED(_c);
    Q_UNUSED(_a);
}

QT_INIT_METAOBJECT const QMetaObject QCPAxisRect::staticMetaObject = { {
    &QCPLayoutElement::staticMetaObject,
    qt_meta_stringdata_QCPAxisRect.data,
    qt_meta_data_QCPAxisRect,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *QCPAxisRect::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *QCPAxisRect::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_QCPAxisRect.stringdata0))
        return static_cast<void*>(this);
    return QCPLayoutElement::qt_metacast(_clname);
}

int QCPAxisRect::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QCPLayoutElement::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    
#ifndef QT_NO_PROPERTIES
    if (_c == QMetaObject::ReadProperty || _c == QMetaObject::WriteProperty
            || _c == QMetaObject::ResetProperty || _c == QMetaObject::RegisterPropertyMetaType) {
        qt_static_metacall(this, _c, _id, _a);
        _id -= 5;
    } else if (_c == QMetaObject::QueryPropertyDesignable) {
        _id -= 5;
    } else if (_c == QMetaObject::QueryPropertyScriptable) {
        _id -= 5;
    } else if (_c == QMetaObject::QueryPropertyStored) {
        _id -= 5;
    } else if (_c == QMetaObject::QueryPropertyEditable) {
        _id -= 5;
    } else if (_c == QMetaObject::QueryPropertyUser) {
        _id -= 5;
    }
#endif // QT_NO_PROPERTIES
    return _id;
}
struct qt_meta_stringdata_QCPAbstractLegendItem_t {
    QByteArrayData data[14];
    char stringdata0[174];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_QCPAbstractLegendItem_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_QCPAbstractLegendItem_t qt_meta_stringdata_QCPAbstractLegendItem = {
    {
QT_MOC_LITERAL(0, 0, 21), // "QCPAbstractLegendItem"
QT_MOC_LITERAL(1, 22, 16), // "selectionChanged"
QT_MOC_LITERAL(2, 39, 0), // ""
QT_MOC_LITERAL(3, 40, 8), // "selected"
QT_MOC_LITERAL(4, 49, 17), // "selectableChanged"
QT_MOC_LITERAL(5, 67, 10), // "selectable"
QT_MOC_LITERAL(6, 78, 13), // "setSelectable"
QT_MOC_LITERAL(7, 92, 11), // "setSelected"
QT_MOC_LITERAL(8, 104, 12), // "parentLegend"
QT_MOC_LITERAL(9, 117, 10), // "QCPLegend*"
QT_MOC_LITERAL(10, 128, 4), // "font"
QT_MOC_LITERAL(11, 133, 9), // "textColor"
QT_MOC_LITERAL(12, 143, 12), // "selectedFont"
QT_MOC_LITERAL(13, 156, 17) // "selectedTextColor"

    },
    "QCPAbstractLegendItem\0selectionChanged\0"
    "\0selected\0selectableChanged\0selectable\0"
    "setSelectable\0setSelected\0parentLegend\0"
    "QCPLegend*\0font\0textColor\0selectedFont\0"
    "selectedTextColor"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_QCPAbstractLegendItem[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
       4,   14, // methods
       7,   46, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       2,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    1,   34,    2, 0x06 /* Public */,
       4,    1,   37,    2, 0x06 /* Public */,

 // slots: name, argc, parameters, tag, flags
       6,    1,   40,    2, 0x0a /* Public */,
       7,    1,   43,    2, 0x0a /* Public */,

 // signals: parameters
    QMetaType::Void, QMetaType::Bool,    3,
    QMetaType::Void, QMetaType::Bool,    5,

 // slots: parameters
    QMetaType::Void, QMetaType::Bool,    5,
    QMetaType::Void, QMetaType::Bool,    3,

 // properties: name, type, flags
       8, 0x80000000 | 9, 0x00095009,
      10, QMetaType::QFont, 0x00095103,
      11, QMetaType::QColor, 0x00095103,
      12, QMetaType::QFont, 0x00095103,
      13, QMetaType::QColor, 0x00095103,
       5, QMetaType::Bool, 0x00495103,
       3, QMetaType::Bool, 0x00495103,

 // properties: notify_signal_id
       0,
       0,
       0,
       0,
       0,
       0,
       1,

       0        // eod
};

void QCPAbstractLegendItem::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<QCPAbstractLegendItem *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->selectionChanged((*reinterpret_cast< bool(*)>(_a[1]))); break;
        case 1: _t->selectableChanged((*reinterpret_cast< bool(*)>(_a[1]))); break;
        case 2: _t->setSelectable((*reinterpret_cast< bool(*)>(_a[1]))); break;
        case 3: _t->setSelected((*reinterpret_cast< bool(*)>(_a[1]))); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (QCPAbstractLegendItem::*)(bool );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&QCPAbstractLegendItem::selectionChanged)) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (QCPAbstractLegendItem::*)(bool );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&QCPAbstractLegendItem::selectableChanged)) {
                *result = 1;
                return;
            }
        }
    } else if (_c == QMetaObject::RegisterPropertyMetaType) {
        switch (_id) {
        default: *reinterpret_cast<int*>(_a[0]) = -1; break;
        case 0:
            *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< QCPLegend* >(); break;
        }
    }

#ifndef QT_NO_PROPERTIES
    else if (_c == QMetaObject::ReadProperty) {
        auto *_t = static_cast<QCPAbstractLegendItem *>(_o);
        Q_UNUSED(_t)
        void *_v = _a[0];
        switch (_id) {
        case 0: *reinterpret_cast< QCPLegend**>(_v) = _t->parentLegend(); break;
        case 1: *reinterpret_cast< QFont*>(_v) = _t->font(); break;
        case 2: *reinterpret_cast< QColor*>(_v) = _t->textColor(); break;
        case 3: *reinterpret_cast< QFont*>(_v) = _t->selectedFont(); break;
        case 4: *reinterpret_cast< QColor*>(_v) = _t->selectedTextColor(); break;
        case 5: *reinterpret_cast< bool*>(_v) = _t->selectable(); break;
        case 6: *reinterpret_cast< bool*>(_v) = _t->selected(); break;
        default: break;
        }
    } else if (_c == QMetaObject::WriteProperty) {
        auto *_t = static_cast<QCPAbstractLegendItem *>(_o);
        Q_UNUSED(_t)
        void *_v = _a[0];
        switch (_id) {
        case 1: _t->setFont(*reinterpret_cast< QFont*>(_v)); break;
        case 2: _t->setTextColor(*reinterpret_cast< QColor*>(_v)); break;
        case 3: _t->setSelectedFont(*reinterpret_cast< QFont*>(_v)); break;
        case 4: _t->setSelectedTextColor(*reinterpret_cast< QColor*>(_v)); break;
        case 5: _t->setSelectable(*reinterpret_cast< bool*>(_v)); break;
        case 6: _t->setSelected(*reinterpret_cast< bool*>(_v)); break;
        default: break;
        }
    } else if (_c == QMetaObject::ResetProperty) {
    }
#endif // QT_NO_PROPERTIES
}

QT_INIT_METAOBJECT const QMetaObject QCPAbstractLegendItem::staticMetaObject = { {
    &QCPLayoutElement::staticMetaObject,
    qt_meta_stringdata_QCPAbstractLegendItem.data,
    qt_meta_data_QCPAbstractLegendItem,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *QCPAbstractLegendItem::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *QCPAbstractLegendItem::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_QCPAbstractLegendItem.stringdata0))
        return static_cast<void*>(this);
    return QCPLayoutElement::qt_metacast(_clname);
}

int QCPAbstractLegendItem::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QCPLayoutElement::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 4)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 4;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 4)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 4;
    }
#ifndef QT_NO_PROPERTIES
    else if (_c == QMetaObject::ReadProperty || _c == QMetaObject::WriteProperty
            || _c == QMetaObject::ResetProperty || _c == QMetaObject::RegisterPropertyMetaType) {
        qt_static_metacall(this, _c, _id, _a);
        _id -= 7;
    } else if (_c == QMetaObject::QueryPropertyDesignable) {
        _id -= 7;
    } else if (_c == QMetaObject::QueryPropertyScriptable) {
        _id -= 7;
    } else if (_c == QMetaObject::QueryPropertyStored) {
        _id -= 7;
    } else if (_c == QMetaObject::QueryPropertyEditable) {
        _id -= 7;
    } else if (_c == QMetaObject::QueryPropertyUser) {
        _id -= 7;
    }
#endif // QT_NO_PROPERTIES
    return _id;
}

// SIGNAL 0
void QCPAbstractLegendItem::selectionChanged(bool _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}

// SIGNAL 1
void QCPAbstractLegendItem::selectableChanged(bool _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 1, _a);
}
struct qt_meta_stringdata_QCPPlottableLegendItem_t {
    QByteArrayData data[1];
    char stringdata0[23];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_QCPPlottableLegendItem_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_QCPPlottableLegendItem_t qt_meta_stringdata_QCPPlottableLegendItem = {
    {
QT_MOC_LITERAL(0, 0, 22) // "QCPPlottableLegendItem"

    },
    "QCPPlottableLegendItem"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_QCPPlottableLegendItem[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
       0,    0, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

       0        // eod
};

void QCPPlottableLegendItem::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    Q_UNUSED(_o);
    Q_UNUSED(_id);
    Q_UNUSED(_c);
    Q_UNUSED(_a);
}

QT_INIT_METAOBJECT const QMetaObject QCPPlottableLegendItem::staticMetaObject = { {
    &QCPAbstractLegendItem::staticMetaObject,
    qt_meta_stringdata_QCPPlottableLegendItem.data,
    qt_meta_data_QCPPlottableLegendItem,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *QCPPlottableLegendItem::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *QCPPlottableLegendItem::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_QCPPlottableLegendItem.stringdata0))
        return static_cast<void*>(this);
    return QCPAbstractLegendItem::qt_metacast(_clname);
}

int QCPPlottableLegendItem::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QCPAbstractLegendItem::qt_metacall(_c, _id, _a);
    return _id;
}
struct qt_meta_stringdata_QCPLegend_t {
    QByteArrayData data[27];
    char stringdata0[358];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_QCPLegend_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_QCPLegend_t qt_meta_stringdata_QCPLegend = {
    {
QT_MOC_LITERAL(0, 0, 9), // "QCPLegend"
QT_MOC_LITERAL(1, 10, 16), // "selectionChanged"
QT_MOC_LITERAL(2, 27, 0), // ""
QT_MOC_LITERAL(3, 28, 26), // "QCPLegend::SelectableParts"
QT_MOC_LITERAL(4, 55, 5), // "parts"
QT_MOC_LITERAL(5, 61, 17), // "selectableChanged"
QT_MOC_LITERAL(6, 79, 18), // "setSelectableParts"
QT_MOC_LITERAL(7, 98, 15), // "SelectableParts"
QT_MOC_LITERAL(8, 114, 15), // "selectableParts"
QT_MOC_LITERAL(9, 130, 16), // "setSelectedParts"
QT_MOC_LITERAL(10, 147, 13), // "selectedParts"
QT_MOC_LITERAL(11, 161, 9), // "borderPen"
QT_MOC_LITERAL(12, 171, 5), // "brush"
QT_MOC_LITERAL(13, 177, 4), // "font"
QT_MOC_LITERAL(14, 182, 9), // "textColor"
QT_MOC_LITERAL(15, 192, 8), // "iconSize"
QT_MOC_LITERAL(16, 201, 15), // "iconTextPadding"
QT_MOC_LITERAL(17, 217, 13), // "iconBorderPen"
QT_MOC_LITERAL(18, 231, 17), // "selectedBorderPen"
QT_MOC_LITERAL(19, 249, 21), // "selectedIconBorderPen"
QT_MOC_LITERAL(20, 271, 13), // "selectedBrush"
QT_MOC_LITERAL(21, 285, 12), // "selectedFont"
QT_MOC_LITERAL(22, 298, 17), // "selectedTextColor"
QT_MOC_LITERAL(23, 316, 14), // "SelectablePart"
QT_MOC_LITERAL(24, 331, 6), // "spNone"
QT_MOC_LITERAL(25, 338, 11), // "spLegendBox"
QT_MOC_LITERAL(26, 350, 7) // "spItems"

    },
    "QCPLegend\0selectionChanged\0\0"
    "QCPLegend::SelectableParts\0parts\0"
    "selectableChanged\0setSelectableParts\0"
    "SelectableParts\0selectableParts\0"
    "setSelectedParts\0selectedParts\0borderPen\0"
    "brush\0font\0textColor\0iconSize\0"
    "iconTextPadding\0iconBorderPen\0"
    "selectedBorderPen\0selectedIconBorderPen\0"
    "selectedBrush\0selectedFont\0selectedTextColor\0"
    "SelectablePart\0spNone\0spLegendBox\0"
    "spItems"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_QCPLegend[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
       4,   14, // methods
      14,   46, // properties
       2,  102, // enums/sets
       0,    0, // constructors
       0,       // flags
       2,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    1,   34,    2, 0x06 /* Public */,
       5,    1,   37,    2, 0x06 /* Public */,

 // slots: name, argc, parameters, tag, flags
       6,    1,   40,    2, 0x0a /* Public */,
       9,    1,   43,    2, 0x0a /* Public */,

 // signals: parameters
    QMetaType::Void, 0x80000000 | 3,    4,
    QMetaType::Void, 0x80000000 | 3,    4,

 // slots: parameters
    QMetaType::Void, 0x80000000 | 7,    8,
    QMetaType::Void, 0x80000000 | 7,   10,

 // properties: name, type, flags
      11, QMetaType::QPen, 0x00095103,
      12, QMetaType::QBrush, 0x00095103,
      13, QMetaType::QFont, 0x00095103,
      14, QMetaType::QColor, 0x00095103,
      15, QMetaType::QSize, 0x00095103,
      16, QMetaType::Int, 0x00095103,
      17, QMetaType::QPen, 0x00095103,
       8, 0x80000000 | 7, 0x0049510b,
      10, 0x80000000 | 7, 0x0049510b,
      18, QMetaType::QPen, 0x00095103,
      19, QMetaType::QPen, 0x00095103,
      20, QMetaType::QBrush, 0x00095103,
      21, QMetaType::QFont, 0x00095103,
      22, QMetaType::QColor, 0x00095103,

 // properties: notify_signal_id
       0,
       0,
       0,
       0,
       0,
       0,
       0,
       0,
       1,
       0,
       0,
       0,
       0,
       0,

 // enums: name, alias, flags, count, data
      23,   23, 0x0,    3,  112,
       7,   23, 0x1,    3,  118,

 // enum data: key, value
      24, uint(QCPLegend::spNone),
      25, uint(QCPLegend::spLegendBox),
      26, uint(QCPLegend::spItems),
      24, uint(QCPLegend::spNone),
      25, uint(QCPLegend::spLegendBox),
      26, uint(QCPLegend::spItems),

       0        // eod
};

void QCPLegend::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<QCPLegend *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->selectionChanged((*reinterpret_cast< QCPLegend::SelectableParts(*)>(_a[1]))); break;
        case 1: _t->selectableChanged((*reinterpret_cast< QCPLegend::SelectableParts(*)>(_a[1]))); break;
        case 2: _t->setSelectableParts((*reinterpret_cast< const SelectableParts(*)>(_a[1]))); break;
        case 3: _t->setSelectedParts((*reinterpret_cast< const SelectableParts(*)>(_a[1]))); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (QCPLegend::*)(QCPLegend::SelectableParts );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&QCPLegend::selectionChanged)) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (QCPLegend::*)(QCPLegend::SelectableParts );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&QCPLegend::selectableChanged)) {
                *result = 1;
                return;
            }
        }
    }
#ifndef QT_NO_PROPERTIES
    else if (_c == QMetaObject::ReadProperty) {
        auto *_t = static_cast<QCPLegend *>(_o);
        Q_UNUSED(_t)
        void *_v = _a[0];
        switch (_id) {
        case 0: *reinterpret_cast< QPen*>(_v) = _t->borderPen(); break;
        case 1: *reinterpret_cast< QBrush*>(_v) = _t->brush(); break;
        case 2: *reinterpret_cast< QFont*>(_v) = _t->font(); break;
        case 3: *reinterpret_cast< QColor*>(_v) = _t->textColor(); break;
        case 4: *reinterpret_cast< QSize*>(_v) = _t->iconSize(); break;
        case 5: *reinterpret_cast< int*>(_v) = _t->iconTextPadding(); break;
        case 6: *reinterpret_cast< QPen*>(_v) = _t->iconBorderPen(); break;
        case 7: *reinterpret_cast<int*>(_v) = QFlag(_t->selectableParts()); break;
        case 8: *reinterpret_cast<int*>(_v) = QFlag(_t->selectedParts()); break;
        case 9: *reinterpret_cast< QPen*>(_v) = _t->selectedBorderPen(); break;
        case 10: *reinterpret_cast< QPen*>(_v) = _t->selectedIconBorderPen(); break;
        case 11: *reinterpret_cast< QBrush*>(_v) = _t->selectedBrush(); break;
        case 12: *reinterpret_cast< QFont*>(_v) = _t->selectedFont(); break;
        case 13: *reinterpret_cast< QColor*>(_v) = _t->selectedTextColor(); break;
        default: break;
        }
    } else if (_c == QMetaObject::WriteProperty) {
        auto *_t = static_cast<QCPLegend *>(_o);
        Q_UNUSED(_t)
        void *_v = _a[0];
        switch (_id) {
        case 0: _t->setBorderPen(*reinterpret_cast< QPen*>(_v)); break;
        case 1: _t->setBrush(*reinterpret_cast< QBrush*>(_v)); break;
        case 2: _t->setFont(*reinterpret_cast< QFont*>(_v)); break;
        case 3: _t->setTextColor(*reinterpret_cast< QColor*>(_v)); break;
        case 4: _t->setIconSize(*reinterpret_cast< QSize*>(_v)); break;
        case 5: _t->setIconTextPadding(*reinterpret_cast< int*>(_v)); break;
        case 6: _t->setIconBorderPen(*reinterpret_cast< QPen*>(_v)); break;
        case 7: _t->setSelectableParts(QFlag(*reinterpret_cast<int*>(_v))); break;
        case 8: _t->setSelectedParts(QFlag(*reinterpret_cast<int*>(_v))); break;
        case 9: _t->setSelectedBorderPen(*reinterpret_cast< QPen*>(_v)); break;
        case 10: _t->setSelectedIconBorderPen(*reinterpret_cast< QPen*>(_v)); break;
        case 11: _t->setSelectedBrush(*reinterpret_cast< QBrush*>(_v)); break;
        case 12: _t->setSelectedFont(*reinterpret_cast< QFont*>(_v)); break;
        case 13: _t->setSelectedTextColor(*reinterpret_cast< QColor*>(_v)); break;
        default: break;
        }
    } else if (_c == QMetaObject::ResetProperty) {
    }
#endif // QT_NO_PROPERTIES
}

QT_INIT_METAOBJECT const QMetaObject QCPLegend::staticMetaObject = { {
    &QCPLayoutGrid::staticMetaObject,
    qt_meta_stringdata_QCPLegend.data,
    qt_meta_data_QCPLegend,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *QCPLegend::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *QCPLegend::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_QCPLegend.stringdata0))
        return static_cast<void*>(this);
    return QCPLayoutGrid::qt_metacast(_clname);
}

int QCPLegend::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QCPLayoutGrid::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 4)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 4;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 4)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 4;
    }
#ifndef QT_NO_PROPERTIES
    else if (_c == QMetaObject::ReadProperty || _c == QMetaObject::WriteProperty
            || _c == QMetaObject::ResetProperty || _c == QMetaObject::RegisterPropertyMetaType) {
        qt_static_metacall(this, _c, _id, _a);
        _id -= 14;
    } else if (_c == QMetaObject::QueryPropertyDesignable) {
        _id -= 14;
    } else if (_c == QMetaObject::QueryPropertyScriptable) {
        _id -= 14;
    } else if (_c == QMetaObject::QueryPropertyStored) {
        _id -= 14;
    } else if (_c == QMetaObject::QueryPropertyEditable) {
        _id -= 14;
    } else if (_c == QMetaObject::QueryPropertyUser) {
        _id -= 14;
    }
#endif // QT_NO_PROPERTIES
    return _id;
}

// SIGNAL 0
void QCPLegend::selectionChanged(QCPLegend::SelectableParts _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}

// SIGNAL 1
void QCPLegend::selectableChanged(QCPLegend::SelectableParts _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 1, _a);
}
struct qt_meta_stringdata_QCPTextElement_t {
    QByteArrayData data[17];
    char stringdata0[189];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_QCPTextElement_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_QCPTextElement_t qt_meta_stringdata_QCPTextElement = {
    {
QT_MOC_LITERAL(0, 0, 14), // "QCPTextElement"
QT_MOC_LITERAL(1, 15, 16), // "selectionChanged"
QT_MOC_LITERAL(2, 32, 0), // ""
QT_MOC_LITERAL(3, 33, 8), // "selected"
QT_MOC_LITERAL(4, 42, 17), // "selectableChanged"
QT_MOC_LITERAL(5, 60, 10), // "selectable"
QT_MOC_LITERAL(6, 71, 7), // "clicked"
QT_MOC_LITERAL(7, 79, 12), // "QMouseEvent*"
QT_MOC_LITERAL(8, 92, 5), // "event"
QT_MOC_LITERAL(9, 98, 13), // "doubleClicked"
QT_MOC_LITERAL(10, 112, 13), // "setSelectable"
QT_MOC_LITERAL(11, 126, 11), // "setSelected"
QT_MOC_LITERAL(12, 138, 4), // "text"
QT_MOC_LITERAL(13, 143, 4), // "font"
QT_MOC_LITERAL(14, 148, 9), // "textColor"
QT_MOC_LITERAL(15, 158, 12), // "selectedFont"
QT_MOC_LITERAL(16, 171, 17) // "selectedTextColor"

    },
    "QCPTextElement\0selectionChanged\0\0"
    "selected\0selectableChanged\0selectable\0"
    "clicked\0QMouseEvent*\0event\0doubleClicked\0"
    "setSelectable\0setSelected\0text\0font\0"
    "textColor\0selectedFont\0selectedTextColor"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_QCPTextElement[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
       6,   14, // methods
       7,   62, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       4,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    1,   44,    2, 0x06 /* Public */,
       4,    1,   47,    2, 0x06 /* Public */,
       6,    1,   50,    2, 0x06 /* Public */,
       9,    1,   53,    2, 0x06 /* Public */,

 // slots: name, argc, parameters, tag, flags
      10,    1,   56,    2, 0x0a /* Public */,
      11,    1,   59,    2, 0x0a /* Public */,

 // signals: parameters
    QMetaType::Void, QMetaType::Bool,    3,
    QMetaType::Void, QMetaType::Bool,    5,
    QMetaType::Void, 0x80000000 | 7,    8,
    QMetaType::Void, 0x80000000 | 7,    8,

 // slots: parameters
    QMetaType::Void, QMetaType::Bool,    5,
    QMetaType::Void, QMetaType::Bool,    3,

 // properties: name, type, flags
      12, QMetaType::QString, 0x00095103,
      13, QMetaType::QFont, 0x00095103,
      14, QMetaType::QColor, 0x00095103,
      15, QMetaType::QFont, 0x00095103,
      16, QMetaType::QColor, 0x00095103,
       5, QMetaType::Bool, 0x00495103,
       3, QMetaType::Bool, 0x00495103,

 // properties: notify_signal_id
       0,
       0,
       0,
       0,
       0,
       1,
       0,

       0        // eod
};

void QCPTextElement::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<QCPTextElement *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->selectionChanged((*reinterpret_cast< bool(*)>(_a[1]))); break;
        case 1: _t->selectableChanged((*reinterpret_cast< bool(*)>(_a[1]))); break;
        case 2: _t->clicked((*reinterpret_cast< QMouseEvent*(*)>(_a[1]))); break;
        case 3: _t->doubleClicked((*reinterpret_cast< QMouseEvent*(*)>(_a[1]))); break;
        case 4: _t->setSelectable((*reinterpret_cast< bool(*)>(_a[1]))); break;
        case 5: _t->setSelected((*reinterpret_cast< bool(*)>(_a[1]))); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (QCPTextElement::*)(bool );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&QCPTextElement::selectionChanged)) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (QCPTextElement::*)(bool );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&QCPTextElement::selectableChanged)) {
                *result = 1;
                return;
            }
        }
        {
            using _t = void (QCPTextElement::*)(QMouseEvent * );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&QCPTextElement::clicked)) {
                *result = 2;
                return;
            }
        }
        {
            using _t = void (QCPTextElement::*)(QMouseEvent * );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&QCPTextElement::doubleClicked)) {
                *result = 3;
                return;
            }
        }
    }
#ifndef QT_NO_PROPERTIES
    else if (_c == QMetaObject::ReadProperty) {
        auto *_t = static_cast<QCPTextElement *>(_o);
        Q_UNUSED(_t)
        void *_v = _a[0];
        switch (_id) {
        case 0: *reinterpret_cast< QString*>(_v) = _t->text(); break;
        case 1: *reinterpret_cast< QFont*>(_v) = _t->font(); break;
        case 2: *reinterpret_cast< QColor*>(_v) = _t->textColor(); break;
        case 3: *reinterpret_cast< QFont*>(_v) = _t->selectedFont(); break;
        case 4: *reinterpret_cast< QColor*>(_v) = _t->selectedTextColor(); break;
        case 5: *reinterpret_cast< bool*>(_v) = _t->selectable(); break;
        case 6: *reinterpret_cast< bool*>(_v) = _t->selected(); break;
        default: break;
        }
    } else if (_c == QMetaObject::WriteProperty) {
        auto *_t = static_cast<QCPTextElement *>(_o);
        Q_UNUSED(_t)
        void *_v = _a[0];
        switch (_id) {
        case 0: _t->setText(*reinterpret_cast< QString*>(_v)); break;
        case 1: _t->setFont(*reinterpret_cast< QFont*>(_v)); break;
        case 2: _t->setTextColor(*reinterpret_cast< QColor*>(_v)); break;
        case 3: _t->setSelectedFont(*reinterpret_cast< QFont*>(_v)); break;
        case 4: _t->setSelectedTextColor(*reinterpret_cast< QColor*>(_v)); break;
        case 5: _t->setSelectable(*reinterpret_cast< bool*>(_v)); break;
        case 6: _t->setSelected(*reinterpret_cast< bool*>(_v)); break;
        default: break;
        }
    } else if (_c == QMetaObject::ResetProperty) {
    }
#endif // QT_NO_PROPERTIES
}

QT_INIT_METAOBJECT const QMetaObject QCPTextElement::staticMetaObject = { {
    &QCPLayoutElement::staticMetaObject,
    qt_meta_stringdata_QCPTextElement.data,
    qt_meta_data_QCPTextElement,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *QCPTextElement::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *QCPTextElement::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_QCPTextElement.stringdata0))
        return static_cast<void*>(this);
    return QCPLayoutElement::qt_metacast(_clname);
}

int QCPTextElement::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QCPLayoutElement::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 6)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 6;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 6)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 6;
    }
#ifndef QT_NO_PROPERTIES
    else if (_c == QMetaObject::ReadProperty || _c == QMetaObject::WriteProperty
            || _c == QMetaObject::ResetProperty || _c == QMetaObject::RegisterPropertyMetaType) {
        qt_static_metacall(this, _c, _id, _a);
        _id -= 7;
    } else if (_c == QMetaObject::QueryPropertyDesignable) {
        _id -= 7;
    } else if (_c == QMetaObject::QueryPropertyScriptable) {
        _id -= 7;
    } else if (_c == QMetaObject::QueryPropertyStored) {
        _id -= 7;
    } else if (_c == QMetaObject::QueryPropertyEditable) {
        _id -= 7;
    } else if (_c == QMetaObject::QueryPropertyUser) {
        _id -= 7;
    }
#endif // QT_NO_PROPERTIES
    return _id;
}

// SIGNAL 0
void QCPTextElement::selectionChanged(bool _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}

// SIGNAL 1
void QCPTextElement::selectableChanged(bool _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 1, _a);
}

// SIGNAL 2
void QCPTextElement::clicked(QMouseEvent * _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 2, _a);
}

// SIGNAL 3
void QCPTextElement::doubleClicked(QMouseEvent * _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 3, _a);
}
struct qt_meta_stringdata_QCPColorScaleAxisRectPrivate_t {
    QByteArrayData data[7];
    char stringdata0[128];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_QCPColorScaleAxisRectPrivate_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_QCPColorScaleAxisRectPrivate_t qt_meta_stringdata_QCPColorScaleAxisRectPrivate = {
    {
QT_MOC_LITERAL(0, 0, 28), // "QCPColorScaleAxisRectPrivate"
QT_MOC_LITERAL(1, 29, 20), // "axisSelectionChanged"
QT_MOC_LITERAL(2, 50, 0), // ""
QT_MOC_LITERAL(3, 51, 24), // "QCPAxis::SelectableParts"
QT_MOC_LITERAL(4, 76, 13), // "selectedParts"
QT_MOC_LITERAL(5, 90, 21), // "axisSelectableChanged"
QT_MOC_LITERAL(6, 112, 15) // "selectableParts"

    },
    "QCPColorScaleAxisRectPrivate\0"
    "axisSelectionChanged\0\0QCPAxis::SelectableParts\0"
    "selectedParts\0axisSelectableChanged\0"
    "selectableParts"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_QCPColorScaleAxisRectPrivate[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
       2,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

 // slots: name, argc, parameters, tag, flags
       1,    1,   24,    2, 0x09 /* Protected */,
       5,    1,   27,    2, 0x09 /* Protected */,

 // slots: parameters
    QMetaType::Void, 0x80000000 | 3,    4,
    QMetaType::Void, 0x80000000 | 3,    6,

       0        // eod
};

void QCPColorScaleAxisRectPrivate::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<QCPColorScaleAxisRectPrivate *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->axisSelectionChanged((*reinterpret_cast< QCPAxis::SelectableParts(*)>(_a[1]))); break;
        case 1: _t->axisSelectableChanged((*reinterpret_cast< QCPAxis::SelectableParts(*)>(_a[1]))); break;
        default: ;
        }
    }
}

QT_INIT_METAOBJECT const QMetaObject QCPColorScaleAxisRectPrivate::staticMetaObject = { {
    &QCPAxisRect::staticMetaObject,
    qt_meta_stringdata_QCPColorScaleAxisRectPrivate.data,
    qt_meta_data_QCPColorScaleAxisRectPrivate,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *QCPColorScaleAxisRectPrivate::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *QCPColorScaleAxisRectPrivate::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_QCPColorScaleAxisRectPrivate.stringdata0))
        return static_cast<void*>(this);
    return QCPAxisRect::qt_metacast(_clname);
}

int QCPColorScaleAxisRectPrivate::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QCPAxisRect::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 2)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 2;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 2)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 2;
    }
    return _id;
}
struct qt_meta_stringdata_QCPColorScale_t {
    QByteArrayData data[23];
    char stringdata0[278];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_QCPColorScale_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_QCPColorScale_t qt_meta_stringdata_QCPColorScale = {
    {
QT_MOC_LITERAL(0, 0, 13), // "QCPColorScale"
QT_MOC_LITERAL(1, 14, 16), // "dataRangeChanged"
QT_MOC_LITERAL(2, 31, 0), // ""
QT_MOC_LITERAL(3, 32, 8), // "QCPRange"
QT_MOC_LITERAL(4, 41, 8), // "newRange"
QT_MOC_LITERAL(5, 50, 20), // "dataScaleTypeChanged"
QT_MOC_LITERAL(6, 71, 18), // "QCPAxis::ScaleType"
QT_MOC_LITERAL(7, 90, 9), // "scaleType"
QT_MOC_LITERAL(8, 100, 15), // "gradientChanged"
QT_MOC_LITERAL(9, 116, 16), // "QCPColorGradient"
QT_MOC_LITERAL(10, 133, 11), // "newGradient"
QT_MOC_LITERAL(11, 145, 12), // "setDataRange"
QT_MOC_LITERAL(12, 158, 9), // "dataRange"
QT_MOC_LITERAL(13, 168, 16), // "setDataScaleType"
QT_MOC_LITERAL(14, 185, 11), // "setGradient"
QT_MOC_LITERAL(15, 197, 8), // "gradient"
QT_MOC_LITERAL(16, 206, 4), // "type"
QT_MOC_LITERAL(17, 211, 17), // "QCPAxis::AxisType"
QT_MOC_LITERAL(18, 229, 13), // "dataScaleType"
QT_MOC_LITERAL(19, 243, 5), // "label"
QT_MOC_LITERAL(20, 249, 8), // "barWidth"
QT_MOC_LITERAL(21, 258, 9), // "rangeDrag"
QT_MOC_LITERAL(22, 268, 9) // "rangeZoom"

    },
    "QCPColorScale\0dataRangeChanged\0\0"
    "QCPRange\0newRange\0dataScaleTypeChanged\0"
    "QCPAxis::ScaleType\0scaleType\0"
    "gradientChanged\0QCPColorGradient\0"
    "newGradient\0setDataRange\0dataRange\0"
    "setDataScaleType\0setGradient\0gradient\0"
    "type\0QCPAxis::AxisType\0dataScaleType\0"
    "label\0barWidth\0rangeDrag\0rangeZoom"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_QCPColorScale[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
       6,   14, // methods
       8,   62, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       3,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    1,   44,    2, 0x06 /* Public */,
       5,    1,   47,    2, 0x06 /* Public */,
       8,    1,   50,    2, 0x06 /* Public */,

 // slots: name, argc, parameters, tag, flags
      11,    1,   53,    2, 0x0a /* Public */,
      13,    1,   56,    2, 0x0a /* Public */,
      14,    1,   59,    2, 0x0a /* Public */,

 // signals: parameters
    QMetaType::Void, 0x80000000 | 3,    4,
    QMetaType::Void, 0x80000000 | 6,    7,
    QMetaType::Void, 0x80000000 | 9,   10,

 // slots: parameters
    QMetaType::Void, 0x80000000 | 3,   12,
    QMetaType::Void, 0x80000000 | 6,    7,
    QMetaType::Void, 0x80000000 | 9,   15,

 // properties: name, type, flags
      16, 0x80000000 | 17, 0x0009510b,
      12, 0x80000000 | 3, 0x0049510b,
      18, 0x80000000 | 6, 0x0049510b,
      15, 0x80000000 | 9, 0x0049510b,
      19, QMetaType::QString, 0x00095103,
      20, QMetaType::Int, 0x00095103,
      21, QMetaType::Bool, 0x00095103,
      22, QMetaType::Bool, 0x00095103,

 // properties: notify_signal_id
       0,
       0,
       1,
       2,
       0,
       0,
       0,
       0,

       0        // eod
};

void QCPColorScale::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<QCPColorScale *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->dataRangeChanged((*reinterpret_cast< const QCPRange(*)>(_a[1]))); break;
        case 1: _t->dataScaleTypeChanged((*reinterpret_cast< QCPAxis::ScaleType(*)>(_a[1]))); break;
        case 2: _t->gradientChanged((*reinterpret_cast< const QCPColorGradient(*)>(_a[1]))); break;
        case 3: _t->setDataRange((*reinterpret_cast< const QCPRange(*)>(_a[1]))); break;
        case 4: _t->setDataScaleType((*reinterpret_cast< QCPAxis::ScaleType(*)>(_a[1]))); break;
        case 5: _t->setGradient((*reinterpret_cast< const QCPColorGradient(*)>(_a[1]))); break;
        default: ;
        }
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        switch (_id) {
        default: *reinterpret_cast<int*>(_a[0]) = -1; break;
        case 1:
            switch (*reinterpret_cast<int*>(_a[1])) {
            default: *reinterpret_cast<int*>(_a[0]) = -1; break;
            case 0:
                *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< QCPAxis::ScaleType >(); break;
            }
            break;
        case 4:
            switch (*reinterpret_cast<int*>(_a[1])) {
            default: *reinterpret_cast<int*>(_a[0]) = -1; break;
            case 0:
                *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< QCPAxis::ScaleType >(); break;
            }
            break;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (QCPColorScale::*)(const QCPRange & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&QCPColorScale::dataRangeChanged)) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (QCPColorScale::*)(QCPAxis::ScaleType );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&QCPColorScale::dataScaleTypeChanged)) {
                *result = 1;
                return;
            }
        }
        {
            using _t = void (QCPColorScale::*)(const QCPColorGradient & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&QCPColorScale::gradientChanged)) {
                *result = 2;
                return;
            }
        }
    } else if (_c == QMetaObject::RegisterPropertyMetaType) {
        switch (_id) {
        default: *reinterpret_cast<int*>(_a[0]) = -1; break;
        case 0:
            *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< QCPAxis::AxisType >(); break;
        case 2:
            *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< QCPAxis::ScaleType >(); break;
        }
    }

#ifndef QT_NO_PROPERTIES
    else if (_c == QMetaObject::ReadProperty) {
        auto *_t = static_cast<QCPColorScale *>(_o);
        Q_UNUSED(_t)
        void *_v = _a[0];
        switch (_id) {
        case 0: *reinterpret_cast< QCPAxis::AxisType*>(_v) = _t->type(); break;
        case 1: *reinterpret_cast< QCPRange*>(_v) = _t->dataRange(); break;
        case 2: *reinterpret_cast< QCPAxis::ScaleType*>(_v) = _t->dataScaleType(); break;
        case 3: *reinterpret_cast< QCPColorGradient*>(_v) = _t->gradient(); break;
        case 4: *reinterpret_cast< QString*>(_v) = _t->label(); break;
        case 5: *reinterpret_cast< int*>(_v) = _t->barWidth(); break;
        case 6: *reinterpret_cast< bool*>(_v) = _t->rangeDrag(); break;
        case 7: *reinterpret_cast< bool*>(_v) = _t->rangeZoom(); break;
        default: break;
        }
    } else if (_c == QMetaObject::WriteProperty) {
        auto *_t = static_cast<QCPColorScale *>(_o);
        Q_UNUSED(_t)
        void *_v = _a[0];
        switch (_id) {
        case 0: _t->setType(*reinterpret_cast< QCPAxis::AxisType*>(_v)); break;
        case 1: _t->setDataRange(*reinterpret_cast< QCPRange*>(_v)); break;
        case 2: _t->setDataScaleType(*reinterpret_cast< QCPAxis::ScaleType*>(_v)); break;
        case 3: _t->setGradient(*reinterpret_cast< QCPColorGradient*>(_v)); break;
        case 4: _t->setLabel(*reinterpret_cast< QString*>(_v)); break;
        case 5: _t->setBarWidth(*reinterpret_cast< int*>(_v)); break;
        case 6: _t->setRangeDrag(*reinterpret_cast< bool*>(_v)); break;
        case 7: _t->setRangeZoom(*reinterpret_cast< bool*>(_v)); break;
        default: break;
        }
    } else if (_c == QMetaObject::ResetProperty) {
    }
#endif // QT_NO_PROPERTIES
}

static const QMetaObject * const qt_meta_extradata_QCPColorScale[] = {
        &QCPAxis::staticMetaObject,
    nullptr
};

QT_INIT_METAOBJECT const QMetaObject QCPColorScale::staticMetaObject = { {
    &QCPLayoutElement::staticMetaObject,
    qt_meta_stringdata_QCPColorScale.data,
    qt_meta_data_QCPColorScale,
    qt_static_metacall,
    qt_meta_extradata_QCPColorScale,
    nullptr
} };


const QMetaObject *QCPColorScale::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *QCPColorScale::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_QCPColorScale.stringdata0))
        return static_cast<void*>(this);
    return QCPLayoutElement::qt_metacast(_clname);
}

int QCPColorScale::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QCPLayoutElement::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 6)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 6;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 6)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 6;
    }
#ifndef QT_NO_PROPERTIES
    else if (_c == QMetaObject::ReadProperty || _c == QMetaObject::WriteProperty
            || _c == QMetaObject::ResetProperty || _c == QMetaObject::RegisterPropertyMetaType) {
        qt_static_metacall(this, _c, _id, _a);
        _id -= 8;
    } else if (_c == QMetaObject::QueryPropertyDesignable) {
        _id -= 8;
    } else if (_c == QMetaObject::QueryPropertyScriptable) {
        _id -= 8;
    } else if (_c == QMetaObject::QueryPropertyStored) {
        _id -= 8;
    } else if (_c == QMetaObject::QueryPropertyEditable) {
        _id -= 8;
    } else if (_c == QMetaObject::QueryPropertyUser) {
        _id -= 8;
    }
#endif // QT_NO_PROPERTIES
    return _id;
}

// SIGNAL 0
void QCPColorScale::dataRangeChanged(const QCPRange & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}

// SIGNAL 1
void QCPColorScale::dataScaleTypeChanged(QCPAxis::ScaleType _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 1, _a);
}

// SIGNAL 2
void QCPColorScale::gradientChanged(const QCPColorGradient & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 2, _a);
}
struct qt_meta_stringdata_QCPGraph_t {
    QByteArrayData data[15];
    char stringdata0[174];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_QCPGraph_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_QCPGraph_t qt_meta_stringdata_QCPGraph = {
    {
QT_MOC_LITERAL(0, 0, 8), // "QCPGraph"
QT_MOC_LITERAL(1, 9, 9), // "lineStyle"
QT_MOC_LITERAL(2, 19, 9), // "LineStyle"
QT_MOC_LITERAL(3, 29, 12), // "scatterStyle"
QT_MOC_LITERAL(4, 42, 15), // "QCPScatterStyle"
QT_MOC_LITERAL(5, 58, 11), // "scatterSkip"
QT_MOC_LITERAL(6, 70, 16), // "channelFillGraph"
QT_MOC_LITERAL(7, 87, 9), // "QCPGraph*"
QT_MOC_LITERAL(8, 97, 16), // "adaptiveSampling"
QT_MOC_LITERAL(9, 114, 6), // "lsNone"
QT_MOC_LITERAL(10, 121, 6), // "lsLine"
QT_MOC_LITERAL(11, 128, 10), // "lsStepLeft"
QT_MOC_LITERAL(12, 139, 11), // "lsStepRight"
QT_MOC_LITERAL(13, 151, 12), // "lsStepCenter"
QT_MOC_LITERAL(14, 164, 9) // "lsImpulse"

    },
    "QCPGraph\0lineStyle\0LineStyle\0scatterStyle\0"
    "QCPScatterStyle\0scatterSkip\0"
    "channelFillGraph\0QCPGraph*\0adaptiveSampling\0"
    "lsNone\0lsLine\0lsStepLeft\0lsStepRight\0"
    "lsStepCenter\0lsImpulse"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_QCPGraph[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
       0,    0, // methods
       5,   14, // properties
       1,   29, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

 // properties: name, type, flags
       1, 0x80000000 | 2, 0x0009510b,
       3, 0x80000000 | 4, 0x0009510b,
       5, QMetaType::Int, 0x00095103,
       6, 0x80000000 | 7, 0x0009510b,
       8, QMetaType::Bool, 0x00095103,

 // enums: name, alias, flags, count, data
       2,    2, 0x0,    6,   34,

 // enum data: key, value
       9, uint(QCPGraph::lsNone),
      10, uint(QCPGraph::lsLine),
      11, uint(QCPGraph::lsStepLeft),
      12, uint(QCPGraph::lsStepRight),
      13, uint(QCPGraph::lsStepCenter),
      14, uint(QCPGraph::lsImpulse),

       0        // eod
};

void QCPGraph::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::RegisterPropertyMetaType) {
        switch (_id) {
        default: *reinterpret_cast<int*>(_a[0]) = -1; break;
        case 3:
            *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< QCPGraph* >(); break;
        }
    }

#ifndef QT_NO_PROPERTIES
    else if (_c == QMetaObject::ReadProperty) {
        auto *_t = static_cast<QCPGraph *>(_o);
        Q_UNUSED(_t)
        void *_v = _a[0];
        switch (_id) {
        case 0: *reinterpret_cast< LineStyle*>(_v) = _t->lineStyle(); break;
        case 1: *reinterpret_cast< QCPScatterStyle*>(_v) = _t->scatterStyle(); break;
        case 2: *reinterpret_cast< int*>(_v) = _t->scatterSkip(); break;
        case 3: *reinterpret_cast< QCPGraph**>(_v) = _t->channelFillGraph(); break;
        case 4: *reinterpret_cast< bool*>(_v) = _t->adaptiveSampling(); break;
        default: break;
        }
    } else if (_c == QMetaObject::WriteProperty) {
        auto *_t = static_cast<QCPGraph *>(_o);
        Q_UNUSED(_t)
        void *_v = _a[0];
        switch (_id) {
        case 0: _t->setLineStyle(*reinterpret_cast< LineStyle*>(_v)); break;
        case 1: _t->setScatterStyle(*reinterpret_cast< QCPScatterStyle*>(_v)); break;
        case 2: _t->setScatterSkip(*reinterpret_cast< int*>(_v)); break;
        case 3: _t->setChannelFillGraph(*reinterpret_cast< QCPGraph**>(_v)); break;
        case 4: _t->setAdaptiveSampling(*reinterpret_cast< bool*>(_v)); break;
        default: break;
        }
    } else if (_c == QMetaObject::ResetProperty) {
    }
#endif // QT_NO_PROPERTIES
    Q_UNUSED(_o);
}

QT_INIT_METAOBJECT const QMetaObject QCPGraph::staticMetaObject = { {
    &QCPAbstractPlottable1D<QCPGraphData>::staticMetaObject,
    qt_meta_stringdata_QCPGraph.data,
    qt_meta_data_QCPGraph,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *QCPGraph::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *QCPGraph::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_QCPGraph.stringdata0))
        return static_cast<void*>(this);
    return QCPAbstractPlottable1D<QCPGraphData>::qt_metacast(_clname);
}

int QCPGraph::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QCPAbstractPlottable1D<QCPGraphData>::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    
#ifndef QT_NO_PROPERTIES
    if (_c == QMetaObject::ReadProperty || _c == QMetaObject::WriteProperty
            || _c == QMetaObject::ResetProperty || _c == QMetaObject::RegisterPropertyMetaType) {
        qt_static_metacall(this, _c, _id, _a);
        _id -= 5;
    } else if (_c == QMetaObject::QueryPropertyDesignable) {
        _id -= 5;
    } else if (_c == QMetaObject::QueryPropertyScriptable) {
        _id -= 5;
    } else if (_c == QMetaObject::QueryPropertyStored) {
        _id -= 5;
    } else if (_c == QMetaObject::QueryPropertyEditable) {
        _id -= 5;
    } else if (_c == QMetaObject::QueryPropertyUser) {
        _id -= 5;
    }
#endif // QT_NO_PROPERTIES
    return _id;
}
struct qt_meta_stringdata_QCPCurve_t {
    QByteArrayData data[8];
    char stringdata0[84];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_QCPCurve_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_QCPCurve_t qt_meta_stringdata_QCPCurve = {
    {
QT_MOC_LITERAL(0, 0, 8), // "QCPCurve"
QT_MOC_LITERAL(1, 9, 12), // "scatterStyle"
QT_MOC_LITERAL(2, 22, 15), // "QCPScatterStyle"
QT_MOC_LITERAL(3, 38, 11), // "scatterSkip"
QT_MOC_LITERAL(4, 50, 9), // "lineStyle"
QT_MOC_LITERAL(5, 60, 9), // "LineStyle"
QT_MOC_LITERAL(6, 70, 6), // "lsNone"
QT_MOC_LITERAL(7, 77, 6) // "lsLine"

    },
    "QCPCurve\0scatterStyle\0QCPScatterStyle\0"
    "scatterSkip\0lineStyle\0LineStyle\0lsNone\0"
    "lsLine"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_QCPCurve[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
       0,    0, // methods
       3,   14, // properties
       1,   23, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

 // properties: name, type, flags
       1, 0x80000000 | 2, 0x0009510b,
       3, QMetaType::Int, 0x00095103,
       4, 0x80000000 | 5, 0x0009510b,

 // enums: name, alias, flags, count, data
       5,    5, 0x0,    2,   28,

 // enum data: key, value
       6, uint(QCPCurve::lsNone),
       7, uint(QCPCurve::lsLine),

       0        // eod
};

void QCPCurve::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{

#ifndef QT_NO_PROPERTIES
    if (_c == QMetaObject::ReadProperty) {
        auto *_t = static_cast<QCPCurve *>(_o);
        Q_UNUSED(_t)
        void *_v = _a[0];
        switch (_id) {
        case 0: *reinterpret_cast< QCPScatterStyle*>(_v) = _t->scatterStyle(); break;
        case 1: *reinterpret_cast< int*>(_v) = _t->scatterSkip(); break;
        case 2: *reinterpret_cast< LineStyle*>(_v) = _t->lineStyle(); break;
        default: break;
        }
    } else if (_c == QMetaObject::WriteProperty) {
        auto *_t = static_cast<QCPCurve *>(_o);
        Q_UNUSED(_t)
        void *_v = _a[0];
        switch (_id) {
        case 0: _t->setScatterStyle(*reinterpret_cast< QCPScatterStyle*>(_v)); break;
        case 1: _t->setScatterSkip(*reinterpret_cast< int*>(_v)); break;
        case 2: _t->setLineStyle(*reinterpret_cast< LineStyle*>(_v)); break;
        default: break;
        }
    } else if (_c == QMetaObject::ResetProperty) {
    }
#endif // QT_NO_PROPERTIES
    Q_UNUSED(_o);
    Q_UNUSED(_id);
    Q_UNUSED(_c);
    Q_UNUSED(_a);
}

QT_INIT_METAOBJECT const QMetaObject QCPCurve::staticMetaObject = { {
    &QCPAbstractPlottable1D<QCPCurveData>::staticMetaObject,
    qt_meta_stringdata_QCPCurve.data,
    qt_meta_data_QCPCurve,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *QCPCurve::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *QCPCurve::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_QCPCurve.stringdata0))
        return static_cast<void*>(this);
    return QCPAbstractPlottable1D<QCPCurveData>::qt_metacast(_clname);
}

int QCPCurve::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QCPAbstractPlottable1D<QCPCurveData>::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    
#ifndef QT_NO_PROPERTIES
    if (_c == QMetaObject::ReadProperty || _c == QMetaObject::WriteProperty
            || _c == QMetaObject::ResetProperty || _c == QMetaObject::RegisterPropertyMetaType) {
        qt_static_metacall(this, _c, _id, _a);
        _id -= 3;
    } else if (_c == QMetaObject::QueryPropertyDesignable) {
        _id -= 3;
    } else if (_c == QMetaObject::QueryPropertyScriptable) {
        _id -= 3;
    } else if (_c == QMetaObject::QueryPropertyStored) {
        _id -= 3;
    } else if (_c == QMetaObject::QueryPropertyEditable) {
        _id -= 3;
    } else if (_c == QMetaObject::QueryPropertyUser) {
        _id -= 3;
    }
#endif // QT_NO_PROPERTIES
    return _id;
}
struct qt_meta_stringdata_QCPBarsGroup_t {
    QByteArrayData data[7];
    char stringdata0[85];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_QCPBarsGroup_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_QCPBarsGroup_t qt_meta_stringdata_QCPBarsGroup = {
    {
QT_MOC_LITERAL(0, 0, 12), // "QCPBarsGroup"
QT_MOC_LITERAL(1, 13, 11), // "spacingType"
QT_MOC_LITERAL(2, 25, 11), // "SpacingType"
QT_MOC_LITERAL(3, 37, 7), // "spacing"
QT_MOC_LITERAL(4, 45, 10), // "stAbsolute"
QT_MOC_LITERAL(5, 56, 15), // "stAxisRectRatio"
QT_MOC_LITERAL(6, 72, 12) // "stPlotCoords"

    },
    "QCPBarsGroup\0spacingType\0SpacingType\0"
    "spacing\0stAbsolute\0stAxisRectRatio\0"
    "stPlotCoords"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_QCPBarsGroup[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
       0,    0, // methods
       2,   14, // properties
       1,   20, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

 // properties: name, type, flags
       1, 0x80000000 | 2, 0x0009510b,
       3, QMetaType::Double, 0x00095103,

 // enums: name, alias, flags, count, data
       2,    2, 0x0,    3,   25,

 // enum data: key, value
       4, uint(QCPBarsGroup::stAbsolute),
       5, uint(QCPBarsGroup::stAxisRectRatio),
       6, uint(QCPBarsGroup::stPlotCoords),

       0        // eod
};

void QCPBarsGroup::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{

#ifndef QT_NO_PROPERTIES
    if (_c == QMetaObject::ReadProperty) {
        auto *_t = static_cast<QCPBarsGroup *>(_o);
        Q_UNUSED(_t)
        void *_v = _a[0];
        switch (_id) {
        case 0: *reinterpret_cast< SpacingType*>(_v) = _t->spacingType(); break;
        case 1: *reinterpret_cast< double*>(_v) = _t->spacing(); break;
        default: break;
        }
    } else if (_c == QMetaObject::WriteProperty) {
        auto *_t = static_cast<QCPBarsGroup *>(_o);
        Q_UNUSED(_t)
        void *_v = _a[0];
        switch (_id) {
        case 0: _t->setSpacingType(*reinterpret_cast< SpacingType*>(_v)); break;
        case 1: _t->setSpacing(*reinterpret_cast< double*>(_v)); break;
        default: break;
        }
    } else if (_c == QMetaObject::ResetProperty) {
    }
#endif // QT_NO_PROPERTIES
    Q_UNUSED(_o);
    Q_UNUSED(_id);
    Q_UNUSED(_c);
    Q_UNUSED(_a);
}

QT_INIT_METAOBJECT const QMetaObject QCPBarsGroup::staticMetaObject = { {
    &QObject::staticMetaObject,
    qt_meta_stringdata_QCPBarsGroup.data,
    qt_meta_data_QCPBarsGroup,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *QCPBarsGroup::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *QCPBarsGroup::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_QCPBarsGroup.stringdata0))
        return static_cast<void*>(this);
    return QObject::qt_metacast(_clname);
}

int QCPBarsGroup::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QObject::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    
#ifndef QT_NO_PROPERTIES
    if (_c == QMetaObject::ReadProperty || _c == QMetaObject::WriteProperty
            || _c == QMetaObject::ResetProperty || _c == QMetaObject::RegisterPropertyMetaType) {
        qt_static_metacall(this, _c, _id, _a);
        _id -= 2;
    } else if (_c == QMetaObject::QueryPropertyDesignable) {
        _id -= 2;
    } else if (_c == QMetaObject::QueryPropertyScriptable) {
        _id -= 2;
    } else if (_c == QMetaObject::QueryPropertyStored) {
        _id -= 2;
    } else if (_c == QMetaObject::QueryPropertyEditable) {
        _id -= 2;
    } else if (_c == QMetaObject::QueryPropertyUser) {
        _id -= 2;
    }
#endif // QT_NO_PROPERTIES
    return _id;
}
struct qt_meta_stringdata_QCPBars_t {
    QByteArrayData data[14];
    char stringdata0[147];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_QCPBars_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_QCPBars_t qt_meta_stringdata_QCPBars = {
    {
QT_MOC_LITERAL(0, 0, 7), // "QCPBars"
QT_MOC_LITERAL(1, 8, 5), // "width"
QT_MOC_LITERAL(2, 14, 9), // "widthType"
QT_MOC_LITERAL(3, 24, 9), // "WidthType"
QT_MOC_LITERAL(4, 34, 9), // "barsGroup"
QT_MOC_LITERAL(5, 44, 13), // "QCPBarsGroup*"
QT_MOC_LITERAL(6, 58, 9), // "baseValue"
QT_MOC_LITERAL(7, 68, 11), // "stackingGap"
QT_MOC_LITERAL(8, 80, 8), // "barBelow"
QT_MOC_LITERAL(9, 89, 8), // "QCPBars*"
QT_MOC_LITERAL(10, 98, 8), // "barAbove"
QT_MOC_LITERAL(11, 107, 10), // "wtAbsolute"
QT_MOC_LITERAL(12, 118, 15), // "wtAxisRectRatio"
QT_MOC_LITERAL(13, 134, 12) // "wtPlotCoords"

    },
    "QCPBars\0width\0widthType\0WidthType\0"
    "barsGroup\0QCPBarsGroup*\0baseValue\0"
    "stackingGap\0barBelow\0QCPBars*\0barAbove\0"
    "wtAbsolute\0wtAxisRectRatio\0wtPlotCoords"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_QCPBars[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
       0,    0, // methods
       7,   14, // properties
       1,   35, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

 // properties: name, type, flags
       1, QMetaType::Double, 0x00095103,
       2, 0x80000000 | 3, 0x0009510b,
       4, 0x80000000 | 5, 0x0009510b,
       6, QMetaType::Double, 0x00095103,
       7, QMetaType::Double, 0x00095103,
       8, 0x80000000 | 9, 0x00095009,
      10, 0x80000000 | 9, 0x00095009,

 // enums: name, alias, flags, count, data
       3,    3, 0x0,    3,   40,

 // enum data: key, value
      11, uint(QCPBars::wtAbsolute),
      12, uint(QCPBars::wtAxisRectRatio),
      13, uint(QCPBars::wtPlotCoords),

       0        // eod
};

void QCPBars::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::RegisterPropertyMetaType) {
        switch (_id) {
        default: *reinterpret_cast<int*>(_a[0]) = -1; break;
        case 6:
        case 5:
            *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< QCPBars* >(); break;
        case 2:
            *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< QCPBarsGroup* >(); break;
        }
    }

#ifndef QT_NO_PROPERTIES
    else if (_c == QMetaObject::ReadProperty) {
        auto *_t = static_cast<QCPBars *>(_o);
        Q_UNUSED(_t)
        void *_v = _a[0];
        switch (_id) {
        case 0: *reinterpret_cast< double*>(_v) = _t->width(); break;
        case 1: *reinterpret_cast< WidthType*>(_v) = _t->widthType(); break;
        case 2: *reinterpret_cast< QCPBarsGroup**>(_v) = _t->barsGroup(); break;
        case 3: *reinterpret_cast< double*>(_v) = _t->baseValue(); break;
        case 4: *reinterpret_cast< double*>(_v) = _t->stackingGap(); break;
        case 5: *reinterpret_cast< QCPBars**>(_v) = _t->barBelow(); break;
        case 6: *reinterpret_cast< QCPBars**>(_v) = _t->barAbove(); break;
        default: break;
        }
    } else if (_c == QMetaObject::WriteProperty) {
        auto *_t = static_cast<QCPBars *>(_o);
        Q_UNUSED(_t)
        void *_v = _a[0];
        switch (_id) {
        case 0: _t->setWidth(*reinterpret_cast< double*>(_v)); break;
        case 1: _t->setWidthType(*reinterpret_cast< WidthType*>(_v)); break;
        case 2: _t->setBarsGroup(*reinterpret_cast< QCPBarsGroup**>(_v)); break;
        case 3: _t->setBaseValue(*reinterpret_cast< double*>(_v)); break;
        case 4: _t->setStackingGap(*reinterpret_cast< double*>(_v)); break;
        default: break;
        }
    } else if (_c == QMetaObject::ResetProperty) {
    }
#endif // QT_NO_PROPERTIES
    Q_UNUSED(_o);
}

QT_INIT_METAOBJECT const QMetaObject QCPBars::staticMetaObject = { {
    &QCPAbstractPlottable1D<QCPBarsData>::staticMetaObject,
    qt_meta_stringdata_QCPBars.data,
    qt_meta_data_QCPBars,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *QCPBars::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *QCPBars::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_QCPBars.stringdata0))
        return static_cast<void*>(this);
    return QCPAbstractPlottable1D<QCPBarsData>::qt_metacast(_clname);
}

int QCPBars::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QCPAbstractPlottable1D<QCPBarsData>::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    
#ifndef QT_NO_PROPERTIES
    if (_c == QMetaObject::ReadProperty || _c == QMetaObject::WriteProperty
            || _c == QMetaObject::ResetProperty || _c == QMetaObject::RegisterPropertyMetaType) {
        qt_static_metacall(this, _c, _id, _a);
        _id -= 7;
    } else if (_c == QMetaObject::QueryPropertyDesignable) {
        _id -= 7;
    } else if (_c == QMetaObject::QueryPropertyScriptable) {
        _id -= 7;
    } else if (_c == QMetaObject::QueryPropertyStored) {
        _id -= 7;
    } else if (_c == QMetaObject::QueryPropertyEditable) {
        _id -= 7;
    } else if (_c == QMetaObject::QueryPropertyUser) {
        _id -= 7;
    }
#endif // QT_NO_PROPERTIES
    return _id;
}
struct qt_meta_stringdata_QCPStatisticalBox_t {
    QByteArrayData data[9];
    char stringdata0[120];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_QCPStatisticalBox_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_QCPStatisticalBox_t qt_meta_stringdata_QCPStatisticalBox = {
    {
QT_MOC_LITERAL(0, 0, 17), // "QCPStatisticalBox"
QT_MOC_LITERAL(1, 18, 5), // "width"
QT_MOC_LITERAL(2, 24, 12), // "whiskerWidth"
QT_MOC_LITERAL(3, 37, 10), // "whiskerPen"
QT_MOC_LITERAL(4, 48, 13), // "whiskerBarPen"
QT_MOC_LITERAL(5, 62, 18), // "whiskerAntialiased"
QT_MOC_LITERAL(6, 81, 9), // "medianPen"
QT_MOC_LITERAL(7, 91, 12), // "outlierStyle"
QT_MOC_LITERAL(8, 104, 15) // "QCPScatterStyle"

    },
    "QCPStatisticalBox\0width\0whiskerWidth\0"
    "whiskerPen\0whiskerBarPen\0whiskerAntialiased\0"
    "medianPen\0outlierStyle\0QCPScatterStyle"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_QCPStatisticalBox[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
       0,    0, // methods
       7,   14, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

 // properties: name, type, flags
       1, QMetaType::Double, 0x00095103,
       2, QMetaType::Double, 0x00095103,
       3, QMetaType::QPen, 0x00095103,
       4, QMetaType::QPen, 0x00095103,
       5, QMetaType::Bool, 0x00095103,
       6, QMetaType::QPen, 0x00095103,
       7, 0x80000000 | 8, 0x0009510b,

       0        // eod
};

void QCPStatisticalBox::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{

#ifndef QT_NO_PROPERTIES
    if (_c == QMetaObject::ReadProperty) {
        auto *_t = static_cast<QCPStatisticalBox *>(_o);
        Q_UNUSED(_t)
        void *_v = _a[0];
        switch (_id) {
        case 0: *reinterpret_cast< double*>(_v) = _t->width(); break;
        case 1: *reinterpret_cast< double*>(_v) = _t->whiskerWidth(); break;
        case 2: *reinterpret_cast< QPen*>(_v) = _t->whiskerPen(); break;
        case 3: *reinterpret_cast< QPen*>(_v) = _t->whiskerBarPen(); break;
        case 4: *reinterpret_cast< bool*>(_v) = _t->whiskerAntialiased(); break;
        case 5: *reinterpret_cast< QPen*>(_v) = _t->medianPen(); break;
        case 6: *reinterpret_cast< QCPScatterStyle*>(_v) = _t->outlierStyle(); break;
        default: break;
        }
    } else if (_c == QMetaObject::WriteProperty) {
        auto *_t = static_cast<QCPStatisticalBox *>(_o);
        Q_UNUSED(_t)
        void *_v = _a[0];
        switch (_id) {
        case 0: _t->setWidth(*reinterpret_cast< double*>(_v)); break;
        case 1: _t->setWhiskerWidth(*reinterpret_cast< double*>(_v)); break;
        case 2: _t->setWhiskerPen(*reinterpret_cast< QPen*>(_v)); break;
        case 3: _t->setWhiskerBarPen(*reinterpret_cast< QPen*>(_v)); break;
        case 4: _t->setWhiskerAntialiased(*reinterpret_cast< bool*>(_v)); break;
        case 5: _t->setMedianPen(*reinterpret_cast< QPen*>(_v)); break;
        case 6: _t->setOutlierStyle(*reinterpret_cast< QCPScatterStyle*>(_v)); break;
        default: break;
        }
    } else if (_c == QMetaObject::ResetProperty) {
    }
#endif // QT_NO_PROPERTIES
    Q_UNUSED(_o);
    Q_UNUSED(_id);
    Q_UNUSED(_c);
    Q_UNUSED(_a);
}

QT_INIT_METAOBJECT const QMetaObject QCPStatisticalBox::staticMetaObject = { {
    &QCPAbstractPlottable1D<QCPStatisticalBoxData>::staticMetaObject,
    qt_meta_stringdata_QCPStatisticalBox.data,
    qt_meta_data_QCPStatisticalBox,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *QCPStatisticalBox::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *QCPStatisticalBox::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_QCPStatisticalBox.stringdata0))
        return static_cast<void*>(this);
    return QCPAbstractPlottable1D<QCPStatisticalBoxData>::qt_metacast(_clname);
}

int QCPStatisticalBox::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QCPAbstractPlottable1D<QCPStatisticalBoxData>::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    
#ifndef QT_NO_PROPERTIES
    if (_c == QMetaObject::ReadProperty || _c == QMetaObject::WriteProperty
            || _c == QMetaObject::ResetProperty || _c == QMetaObject::RegisterPropertyMetaType) {
        qt_static_metacall(this, _c, _id, _a);
        _id -= 7;
    } else if (_c == QMetaObject::QueryPropertyDesignable) {
        _id -= 7;
    } else if (_c == QMetaObject::QueryPropertyScriptable) {
        _id -= 7;
    } else if (_c == QMetaObject::QueryPropertyStored) {
        _id -= 7;
    } else if (_c == QMetaObject::QueryPropertyEditable) {
        _id -= 7;
    } else if (_c == QMetaObject::QueryPropertyUser) {
        _id -= 7;
    }
#endif // QT_NO_PROPERTIES
    return _id;
}
struct qt_meta_stringdata_QCPColorMap_t {
    QByteArrayData data[25];
    char stringdata0[334];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_QCPColorMap_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_QCPColorMap_t qt_meta_stringdata_QCPColorMap = {
    {
QT_MOC_LITERAL(0, 0, 11), // "QCPColorMap"
QT_MOC_LITERAL(1, 12, 16), // "dataRangeChanged"
QT_MOC_LITERAL(2, 29, 0), // ""
QT_MOC_LITERAL(3, 30, 8), // "QCPRange"
QT_MOC_LITERAL(4, 39, 8), // "newRange"
QT_MOC_LITERAL(5, 48, 20), // "dataScaleTypeChanged"
QT_MOC_LITERAL(6, 69, 18), // "QCPAxis::ScaleType"
QT_MOC_LITERAL(7, 88, 9), // "scaleType"
QT_MOC_LITERAL(8, 98, 15), // "gradientChanged"
QT_MOC_LITERAL(9, 114, 16), // "QCPColorGradient"
QT_MOC_LITERAL(10, 131, 11), // "newGradient"
QT_MOC_LITERAL(11, 143, 12), // "setDataRange"
QT_MOC_LITERAL(12, 156, 9), // "dataRange"
QT_MOC_LITERAL(13, 166, 16), // "setDataScaleType"
QT_MOC_LITERAL(14, 183, 11), // "setGradient"
QT_MOC_LITERAL(15, 195, 8), // "gradient"
QT_MOC_LITERAL(16, 204, 16), // "updateLegendIcon"
QT_MOC_LITERAL(17, 221, 22), // "Qt::TransformationMode"
QT_MOC_LITERAL(18, 244, 13), // "transformMode"
QT_MOC_LITERAL(19, 258, 9), // "thumbSize"
QT_MOC_LITERAL(20, 268, 13), // "dataScaleType"
QT_MOC_LITERAL(21, 282, 11), // "interpolate"
QT_MOC_LITERAL(22, 294, 13), // "tightBoundary"
QT_MOC_LITERAL(23, 308, 10), // "colorScale"
QT_MOC_LITERAL(24, 319, 14) // "QCPColorScale*"

    },
    "QCPColorMap\0dataRangeChanged\0\0QCPRange\0"
    "newRange\0dataScaleTypeChanged\0"
    "QCPAxis::ScaleType\0scaleType\0"
    "gradientChanged\0QCPColorGradient\0"
    "newGradient\0setDataRange\0dataRange\0"
    "setDataScaleType\0setGradient\0gradient\0"
    "updateLegendIcon\0Qt::TransformationMode\0"
    "transformMode\0thumbSize\0dataScaleType\0"
    "interpolate\0tightBoundary\0colorScale\0"
    "QCPColorScale*"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_QCPColorMap[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
       9,   14, // methods
       6,   86, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       3,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    1,   59,    2, 0x06 /* Public */,
       5,    1,   62,    2, 0x06 /* Public */,
       8,    1,   65,    2, 0x06 /* Public */,

 // slots: name, argc, parameters, tag, flags
      11,    1,   68,    2, 0x0a /* Public */,
      13,    1,   71,    2, 0x0a /* Public */,
      14,    1,   74,    2, 0x0a /* Public */,
      16,    2,   77,    2, 0x0a /* Public */,
      16,    1,   82,    2, 0x2a /* Public | MethodCloned */,
      16,    0,   85,    2, 0x2a /* Public | MethodCloned */,

 // signals: parameters
    QMetaType::Void, 0x80000000 | 3,    4,
    QMetaType::Void, 0x80000000 | 6,    7,
    QMetaType::Void, 0x80000000 | 9,   10,

 // slots: parameters
    QMetaType::Void, 0x80000000 | 3,   12,
    QMetaType::Void, 0x80000000 | 6,    7,
    QMetaType::Void, 0x80000000 | 9,   15,
    QMetaType::Void, 0x80000000 | 17, QMetaType::QSize,   18,   19,
    QMetaType::Void, 0x80000000 | 17,   18,
    QMetaType::Void,

 // properties: name, type, flags
      12, 0x80000000 | 3, 0x0049510b,
      20, 0x80000000 | 6, 0x0049510b,
      15, 0x80000000 | 9, 0x0049510b,
      21, QMetaType::Bool, 0x00095103,
      22, QMetaType::Bool, 0x00095103,
      23, 0x80000000 | 24, 0x0009510b,

 // properties: notify_signal_id
       0,
       1,
       2,
       0,
       0,
       0,

       0        // eod
};

void QCPColorMap::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<QCPColorMap *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->dataRangeChanged((*reinterpret_cast< const QCPRange(*)>(_a[1]))); break;
        case 1: _t->dataScaleTypeChanged((*reinterpret_cast< QCPAxis::ScaleType(*)>(_a[1]))); break;
        case 2: _t->gradientChanged((*reinterpret_cast< const QCPColorGradient(*)>(_a[1]))); break;
        case 3: _t->setDataRange((*reinterpret_cast< const QCPRange(*)>(_a[1]))); break;
        case 4: _t->setDataScaleType((*reinterpret_cast< QCPAxis::ScaleType(*)>(_a[1]))); break;
        case 5: _t->setGradient((*reinterpret_cast< const QCPColorGradient(*)>(_a[1]))); break;
        case 6: _t->updateLegendIcon((*reinterpret_cast< Qt::TransformationMode(*)>(_a[1])),(*reinterpret_cast< const QSize(*)>(_a[2]))); break;
        case 7: _t->updateLegendIcon((*reinterpret_cast< Qt::TransformationMode(*)>(_a[1]))); break;
        case 8: _t->updateLegendIcon(); break;
        default: ;
        }
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        switch (_id) {
        default: *reinterpret_cast<int*>(_a[0]) = -1; break;
        case 1:
            switch (*reinterpret_cast<int*>(_a[1])) {
            default: *reinterpret_cast<int*>(_a[0]) = -1; break;
            case 0:
                *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< QCPAxis::ScaleType >(); break;
            }
            break;
        case 4:
            switch (*reinterpret_cast<int*>(_a[1])) {
            default: *reinterpret_cast<int*>(_a[0]) = -1; break;
            case 0:
                *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< QCPAxis::ScaleType >(); break;
            }
            break;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (QCPColorMap::*)(const QCPRange & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&QCPColorMap::dataRangeChanged)) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (QCPColorMap::*)(QCPAxis::ScaleType );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&QCPColorMap::dataScaleTypeChanged)) {
                *result = 1;
                return;
            }
        }
        {
            using _t = void (QCPColorMap::*)(const QCPColorGradient & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&QCPColorMap::gradientChanged)) {
                *result = 2;
                return;
            }
        }
    } else if (_c == QMetaObject::RegisterPropertyMetaType) {
        switch (_id) {
        default: *reinterpret_cast<int*>(_a[0]) = -1; break;
        case 1:
            *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< QCPAxis::ScaleType >(); break;
        case 5:
            *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< QCPColorScale* >(); break;
        }
    }

#ifndef QT_NO_PROPERTIES
    else if (_c == QMetaObject::ReadProperty) {
        auto *_t = static_cast<QCPColorMap *>(_o);
        Q_UNUSED(_t)
        void *_v = _a[0];
        switch (_id) {
        case 0: *reinterpret_cast< QCPRange*>(_v) = _t->dataRange(); break;
        case 1: *reinterpret_cast< QCPAxis::ScaleType*>(_v) = _t->dataScaleType(); break;
        case 2: *reinterpret_cast< QCPColorGradient*>(_v) = _t->gradient(); break;
        case 3: *reinterpret_cast< bool*>(_v) = _t->interpolate(); break;
        case 4: *reinterpret_cast< bool*>(_v) = _t->tightBoundary(); break;
        case 5: *reinterpret_cast< QCPColorScale**>(_v) = _t->colorScale(); break;
        default: break;
        }
    } else if (_c == QMetaObject::WriteProperty) {
        auto *_t = static_cast<QCPColorMap *>(_o);
        Q_UNUSED(_t)
        void *_v = _a[0];
        switch (_id) {
        case 0: _t->setDataRange(*reinterpret_cast< QCPRange*>(_v)); break;
        case 1: _t->setDataScaleType(*reinterpret_cast< QCPAxis::ScaleType*>(_v)); break;
        case 2: _t->setGradient(*reinterpret_cast< QCPColorGradient*>(_v)); break;
        case 3: _t->setInterpolate(*reinterpret_cast< bool*>(_v)); break;
        case 4: _t->setTightBoundary(*reinterpret_cast< bool*>(_v)); break;
        case 5: _t->setColorScale(*reinterpret_cast< QCPColorScale**>(_v)); break;
        default: break;
        }
    } else if (_c == QMetaObject::ResetProperty) {
    }
#endif // QT_NO_PROPERTIES
}

static const QMetaObject * const qt_meta_extradata_QCPColorMap[] = {
        &QCPAxis::staticMetaObject,
    nullptr
};

QT_INIT_METAOBJECT const QMetaObject QCPColorMap::staticMetaObject = { {
    &QCPAbstractPlottable::staticMetaObject,
    qt_meta_stringdata_QCPColorMap.data,
    qt_meta_data_QCPColorMap,
    qt_static_metacall,
    qt_meta_extradata_QCPColorMap,
    nullptr
} };


const QMetaObject *QCPColorMap::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *QCPColorMap::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_QCPColorMap.stringdata0))
        return static_cast<void*>(this);
    return QCPAbstractPlottable::qt_metacast(_clname);
}

int QCPColorMap::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QCPAbstractPlottable::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 9)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 9;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 9)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 9;
    }
#ifndef QT_NO_PROPERTIES
    else if (_c == QMetaObject::ReadProperty || _c == QMetaObject::WriteProperty
            || _c == QMetaObject::ResetProperty || _c == QMetaObject::RegisterPropertyMetaType) {
        qt_static_metacall(this, _c, _id, _a);
        _id -= 6;
    } else if (_c == QMetaObject::QueryPropertyDesignable) {
        _id -= 6;
    } else if (_c == QMetaObject::QueryPropertyScriptable) {
        _id -= 6;
    } else if (_c == QMetaObject::QueryPropertyStored) {
        _id -= 6;
    } else if (_c == QMetaObject::QueryPropertyEditable) {
        _id -= 6;
    } else if (_c == QMetaObject::QueryPropertyUser) {
        _id -= 6;
    }
#endif // QT_NO_PROPERTIES
    return _id;
}

// SIGNAL 0
void QCPColorMap::dataRangeChanged(const QCPRange & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}

// SIGNAL 1
void QCPColorMap::dataScaleTypeChanged(QCPAxis::ScaleType _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 1, _a);
}

// SIGNAL 2
void QCPColorMap::gradientChanged(const QCPColorGradient & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 2, _a);
}
struct qt_meta_stringdata_QCPFinancial_t {
    QByteArrayData data[16];
    char stringdata0[185];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_QCPFinancial_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_QCPFinancial_t qt_meta_stringdata_QCPFinancial = {
    {
QT_MOC_LITERAL(0, 0, 12), // "QCPFinancial"
QT_MOC_LITERAL(1, 13, 10), // "chartStyle"
QT_MOC_LITERAL(2, 24, 10), // "ChartStyle"
QT_MOC_LITERAL(3, 35, 5), // "width"
QT_MOC_LITERAL(4, 41, 9), // "widthType"
QT_MOC_LITERAL(5, 51, 9), // "WidthType"
QT_MOC_LITERAL(6, 61, 10), // "twoColored"
QT_MOC_LITERAL(7, 72, 13), // "brushPositive"
QT_MOC_LITERAL(8, 86, 13), // "brushNegative"
QT_MOC_LITERAL(9, 100, 11), // "penPositive"
QT_MOC_LITERAL(10, 112, 11), // "penNegative"
QT_MOC_LITERAL(11, 124, 10), // "wtAbsolute"
QT_MOC_LITERAL(12, 135, 15), // "wtAxisRectRatio"
QT_MOC_LITERAL(13, 151, 12), // "wtPlotCoords"
QT_MOC_LITERAL(14, 164, 6), // "csOhlc"
QT_MOC_LITERAL(15, 171, 13) // "csCandlestick"

    },
    "QCPFinancial\0chartStyle\0ChartStyle\0"
    "width\0widthType\0WidthType\0twoColored\0"
    "brushPositive\0brushNegative\0penPositive\0"
    "penNegative\0wtAbsolute\0wtAxisRectRatio\0"
    "wtPlotCoords\0csOhlc\0csCandlestick"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_QCPFinancial[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
       0,    0, // methods
       8,   14, // properties
       2,   38, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

 // properties: name, type, flags
       1, 0x80000000 | 2, 0x0009510b,
       3, QMetaType::Double, 0x00095103,
       4, 0x80000000 | 5, 0x0009510b,
       6, QMetaType::Bool, 0x00095103,
       7, QMetaType::QBrush, 0x00095103,
       8, QMetaType::QBrush, 0x00095103,
       9, QMetaType::QPen, 0x00095103,
      10, QMetaType::QPen, 0x00095103,

 // enums: name, alias, flags, count, data
       5,    5, 0x0,    3,   48,
       2,    2, 0x0,    2,   54,

 // enum data: key, value
      11, uint(QCPFinancial::wtAbsolute),
      12, uint(QCPFinancial::wtAxisRectRatio),
      13, uint(QCPFinancial::wtPlotCoords),
      14, uint(QCPFinancial::csOhlc),
      15, uint(QCPFinancial::csCandlestick),

       0        // eod
};

void QCPFinancial::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{

#ifndef QT_NO_PROPERTIES
    if (_c == QMetaObject::ReadProperty) {
        auto *_t = static_cast<QCPFinancial *>(_o);
        Q_UNUSED(_t)
        void *_v = _a[0];
        switch (_id) {
        case 0: *reinterpret_cast< ChartStyle*>(_v) = _t->chartStyle(); break;
        case 1: *reinterpret_cast< double*>(_v) = _t->width(); break;
        case 2: *reinterpret_cast< WidthType*>(_v) = _t->widthType(); break;
        case 3: *reinterpret_cast< bool*>(_v) = _t->twoColored(); break;
        case 4: *reinterpret_cast< QBrush*>(_v) = _t->brushPositive(); break;
        case 5: *reinterpret_cast< QBrush*>(_v) = _t->brushNegative(); break;
        case 6: *reinterpret_cast< QPen*>(_v) = _t->penPositive(); break;
        case 7: *reinterpret_cast< QPen*>(_v) = _t->penNegative(); break;
        default: break;
        }
    } else if (_c == QMetaObject::WriteProperty) {
        auto *_t = static_cast<QCPFinancial *>(_o);
        Q_UNUSED(_t)
        void *_v = _a[0];
        switch (_id) {
        case 0: _t->setChartStyle(*reinterpret_cast< ChartStyle*>(_v)); break;
        case 1: _t->setWidth(*reinterpret_cast< double*>(_v)); break;
        case 2: _t->setWidthType(*reinterpret_cast< WidthType*>(_v)); break;
        case 3: _t->setTwoColored(*reinterpret_cast< bool*>(_v)); break;
        case 4: _t->setBrushPositive(*reinterpret_cast< QBrush*>(_v)); break;
        case 5: _t->setBrushNegative(*reinterpret_cast< QBrush*>(_v)); break;
        case 6: _t->setPenPositive(*reinterpret_cast< QPen*>(_v)); break;
        case 7: _t->setPenNegative(*reinterpret_cast< QPen*>(_v)); break;
        default: break;
        }
    } else if (_c == QMetaObject::ResetProperty) {
    }
#endif // QT_NO_PROPERTIES
    Q_UNUSED(_o);
    Q_UNUSED(_id);
    Q_UNUSED(_c);
    Q_UNUSED(_a);
}

QT_INIT_METAOBJECT const QMetaObject QCPFinancial::staticMetaObject = { {
    &QCPAbstractPlottable1D<QCPFinancialData>::staticMetaObject,
    qt_meta_stringdata_QCPFinancial.data,
    qt_meta_data_QCPFinancial,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *QCPFinancial::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *QCPFinancial::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_QCPFinancial.stringdata0))
        return static_cast<void*>(this);
    return QCPAbstractPlottable1D<QCPFinancialData>::qt_metacast(_clname);
}

int QCPFinancial::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QCPAbstractPlottable1D<QCPFinancialData>::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    
#ifndef QT_NO_PROPERTIES
    if (_c == QMetaObject::ReadProperty || _c == QMetaObject::WriteProperty
            || _c == QMetaObject::ResetProperty || _c == QMetaObject::RegisterPropertyMetaType) {
        qt_static_metacall(this, _c, _id, _a);
        _id -= 8;
    } else if (_c == QMetaObject::QueryPropertyDesignable) {
        _id -= 8;
    } else if (_c == QMetaObject::QueryPropertyScriptable) {
        _id -= 8;
    } else if (_c == QMetaObject::QueryPropertyStored) {
        _id -= 8;
    } else if (_c == QMetaObject::QueryPropertyEditable) {
        _id -= 8;
    } else if (_c == QMetaObject::QueryPropertyUser) {
        _id -= 8;
    }
#endif // QT_NO_PROPERTIES
    return _id;
}
struct qt_meta_stringdata_QCPErrorBars_t {
    QByteArrayData data[11];
    char stringdata0[163];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_QCPErrorBars_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_QCPErrorBars_t qt_meta_stringdata_QCPErrorBars = {
    {
QT_MOC_LITERAL(0, 0, 12), // "QCPErrorBars"
QT_MOC_LITERAL(1, 13, 4), // "data"
QT_MOC_LITERAL(2, 18, 41), // "QSharedPointer<QCPErrorBarsDa..."
QT_MOC_LITERAL(3, 60, 13), // "dataPlottable"
QT_MOC_LITERAL(4, 74, 21), // "QCPAbstractPlottable*"
QT_MOC_LITERAL(5, 96, 9), // "errorType"
QT_MOC_LITERAL(6, 106, 9), // "ErrorType"
QT_MOC_LITERAL(7, 116, 12), // "whiskerWidth"
QT_MOC_LITERAL(8, 129, 9), // "symbolGap"
QT_MOC_LITERAL(9, 139, 10), // "etKeyError"
QT_MOC_LITERAL(10, 150, 12) // "etValueError"

    },
    "QCPErrorBars\0data\0"
    "QSharedPointer<QCPErrorBarsDataContainer>\0"
    "dataPlottable\0QCPAbstractPlottable*\0"
    "errorType\0ErrorType\0whiskerWidth\0"
    "symbolGap\0etKeyError\0etValueError"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_QCPErrorBars[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
       0,    0, // methods
       5,   14, // properties
       1,   29, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

 // properties: name, type, flags
       1, 0x80000000 | 2, 0x0009510b,
       3, 0x80000000 | 4, 0x0009510b,
       5, 0x80000000 | 6, 0x0009510b,
       7, QMetaType::Double, 0x00095103,
       8, QMetaType::Double, 0x00095103,

 // enums: name, alias, flags, count, data
       6,    6, 0x0,    2,   34,

 // enum data: key, value
       9, uint(QCPErrorBars::etKeyError),
      10, uint(QCPErrorBars::etValueError),

       0        // eod
};

void QCPErrorBars::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::RegisterPropertyMetaType) {
        switch (_id) {
        default: *reinterpret_cast<int*>(_a[0]) = -1; break;
        case 1:
            *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< QCPAbstractPlottable* >(); break;
        }
    }

#ifndef QT_NO_PROPERTIES
    else if (_c == QMetaObject::ReadProperty) {
        auto *_t = static_cast<QCPErrorBars *>(_o);
        Q_UNUSED(_t)
        void *_v = _a[0];
        switch (_id) {
        case 0: *reinterpret_cast< QSharedPointer<QCPErrorBarsDataContainer>*>(_v) = _t->data(); break;
        case 1: *reinterpret_cast< QCPAbstractPlottable**>(_v) = _t->dataPlottable(); break;
        case 2: *reinterpret_cast< ErrorType*>(_v) = _t->errorType(); break;
        case 3: *reinterpret_cast< double*>(_v) = _t->whiskerWidth(); break;
        case 4: *reinterpret_cast< double*>(_v) = _t->symbolGap(); break;
        default: break;
        }
    } else if (_c == QMetaObject::WriteProperty) {
        auto *_t = static_cast<QCPErrorBars *>(_o);
        Q_UNUSED(_t)
        void *_v = _a[0];
        switch (_id) {
        case 0: _t->setData(*reinterpret_cast< QSharedPointer<QCPErrorBarsDataContainer>*>(_v)); break;
        case 1: _t->setDataPlottable(*reinterpret_cast< QCPAbstractPlottable**>(_v)); break;
        case 2: _t->setErrorType(*reinterpret_cast< ErrorType*>(_v)); break;
        case 3: _t->setWhiskerWidth(*reinterpret_cast< double*>(_v)); break;
        case 4: _t->setSymbolGap(*reinterpret_cast< double*>(_v)); break;
        default: break;
        }
    } else if (_c == QMetaObject::ResetProperty) {
    }
#endif // QT_NO_PROPERTIES
    Q_UNUSED(_o);
}

QT_INIT_METAOBJECT const QMetaObject QCPErrorBars::staticMetaObject = { {
    &QCPAbstractPlottable::staticMetaObject,
    qt_meta_stringdata_QCPErrorBars.data,
    qt_meta_data_QCPErrorBars,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *QCPErrorBars::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *QCPErrorBars::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_QCPErrorBars.stringdata0))
        return static_cast<void*>(this);
    if (!strcmp(_clname, "QCPPlottableInterface1D"))
        return static_cast< QCPPlottableInterface1D*>(this);
    return QCPAbstractPlottable::qt_metacast(_clname);
}

int QCPErrorBars::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QCPAbstractPlottable::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    
#ifndef QT_NO_PROPERTIES
    if (_c == QMetaObject::ReadProperty || _c == QMetaObject::WriteProperty
            || _c == QMetaObject::ResetProperty || _c == QMetaObject::RegisterPropertyMetaType) {
        qt_static_metacall(this, _c, _id, _a);
        _id -= 5;
    } else if (_c == QMetaObject::QueryPropertyDesignable) {
        _id -= 5;
    } else if (_c == QMetaObject::QueryPropertyScriptable) {
        _id -= 5;
    } else if (_c == QMetaObject::QueryPropertyStored) {
        _id -= 5;
    } else if (_c == QMetaObject::QueryPropertyEditable) {
        _id -= 5;
    } else if (_c == QMetaObject::QueryPropertyUser) {
        _id -= 5;
    }
#endif // QT_NO_PROPERTIES
    return _id;
}
struct qt_meta_stringdata_QCPItemStraightLine_t {
    QByteArrayData data[3];
    char stringdata0[36];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_QCPItemStraightLine_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_QCPItemStraightLine_t qt_meta_stringdata_QCPItemStraightLine = {
    {
QT_MOC_LITERAL(0, 0, 19), // "QCPItemStraightLine"
QT_MOC_LITERAL(1, 20, 3), // "pen"
QT_MOC_LITERAL(2, 24, 11) // "selectedPen"

    },
    "QCPItemStraightLine\0pen\0selectedPen"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_QCPItemStraightLine[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
       0,    0, // methods
       2,   14, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

 // properties: name, type, flags
       1, QMetaType::QPen, 0x00095103,
       2, QMetaType::QPen, 0x00095103,

       0        // eod
};

void QCPItemStraightLine::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{

#ifndef QT_NO_PROPERTIES
    if (_c == QMetaObject::ReadProperty) {
        auto *_t = static_cast<QCPItemStraightLine *>(_o);
        Q_UNUSED(_t)
        void *_v = _a[0];
        switch (_id) {
        case 0: *reinterpret_cast< QPen*>(_v) = _t->pen(); break;
        case 1: *reinterpret_cast< QPen*>(_v) = _t->selectedPen(); break;
        default: break;
        }
    } else if (_c == QMetaObject::WriteProperty) {
        auto *_t = static_cast<QCPItemStraightLine *>(_o);
        Q_UNUSED(_t)
        void *_v = _a[0];
        switch (_id) {
        case 0: _t->setPen(*reinterpret_cast< QPen*>(_v)); break;
        case 1: _t->setSelectedPen(*reinterpret_cast< QPen*>(_v)); break;
        default: break;
        }
    } else if (_c == QMetaObject::ResetProperty) {
    }
#endif // QT_NO_PROPERTIES
    Q_UNUSED(_o);
    Q_UNUSED(_id);
    Q_UNUSED(_c);
    Q_UNUSED(_a);
}

QT_INIT_METAOBJECT const QMetaObject QCPItemStraightLine::staticMetaObject = { {
    &QCPAbstractItem::staticMetaObject,
    qt_meta_stringdata_QCPItemStraightLine.data,
    qt_meta_data_QCPItemStraightLine,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *QCPItemStraightLine::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *QCPItemStraightLine::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_QCPItemStraightLine.stringdata0))
        return static_cast<void*>(this);
    return QCPAbstractItem::qt_metacast(_clname);
}

int QCPItemStraightLine::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QCPAbstractItem::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    
#ifndef QT_NO_PROPERTIES
    if (_c == QMetaObject::ReadProperty || _c == QMetaObject::WriteProperty
            || _c == QMetaObject::ResetProperty || _c == QMetaObject::RegisterPropertyMetaType) {
        qt_static_metacall(this, _c, _id, _a);
        _id -= 2;
    } else if (_c == QMetaObject::QueryPropertyDesignable) {
        _id -= 2;
    } else if (_c == QMetaObject::QueryPropertyScriptable) {
        _id -= 2;
    } else if (_c == QMetaObject::QueryPropertyStored) {
        _id -= 2;
    } else if (_c == QMetaObject::QueryPropertyEditable) {
        _id -= 2;
    } else if (_c == QMetaObject::QueryPropertyUser) {
        _id -= 2;
    }
#endif // QT_NO_PROPERTIES
    return _id;
}
struct qt_meta_stringdata_QCPItemLine_t {
    QByteArrayData data[6];
    char stringdata0[52];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_QCPItemLine_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_QCPItemLine_t qt_meta_stringdata_QCPItemLine = {
    {
QT_MOC_LITERAL(0, 0, 11), // "QCPItemLine"
QT_MOC_LITERAL(1, 12, 3), // "pen"
QT_MOC_LITERAL(2, 16, 11), // "selectedPen"
QT_MOC_LITERAL(3, 28, 4), // "head"
QT_MOC_LITERAL(4, 33, 13), // "QCPLineEnding"
QT_MOC_LITERAL(5, 47, 4) // "tail"

    },
    "QCPItemLine\0pen\0selectedPen\0head\0"
    "QCPLineEnding\0tail"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_QCPItemLine[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
       0,    0, // methods
       4,   14, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

 // properties: name, type, flags
       1, QMetaType::QPen, 0x00095103,
       2, QMetaType::QPen, 0x00095103,
       3, 0x80000000 | 4, 0x0009510b,
       5, 0x80000000 | 4, 0x0009510b,

       0        // eod
};

void QCPItemLine::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{

#ifndef QT_NO_PROPERTIES
    if (_c == QMetaObject::ReadProperty) {
        auto *_t = static_cast<QCPItemLine *>(_o);
        Q_UNUSED(_t)
        void *_v = _a[0];
        switch (_id) {
        case 0: *reinterpret_cast< QPen*>(_v) = _t->pen(); break;
        case 1: *reinterpret_cast< QPen*>(_v) = _t->selectedPen(); break;
        case 2: *reinterpret_cast< QCPLineEnding*>(_v) = _t->head(); break;
        case 3: *reinterpret_cast< QCPLineEnding*>(_v) = _t->tail(); break;
        default: break;
        }
    } else if (_c == QMetaObject::WriteProperty) {
        auto *_t = static_cast<QCPItemLine *>(_o);
        Q_UNUSED(_t)
        void *_v = _a[0];
        switch (_id) {
        case 0: _t->setPen(*reinterpret_cast< QPen*>(_v)); break;
        case 1: _t->setSelectedPen(*reinterpret_cast< QPen*>(_v)); break;
        case 2: _t->setHead(*reinterpret_cast< QCPLineEnding*>(_v)); break;
        case 3: _t->setTail(*reinterpret_cast< QCPLineEnding*>(_v)); break;
        default: break;
        }
    } else if (_c == QMetaObject::ResetProperty) {
    }
#endif // QT_NO_PROPERTIES
    Q_UNUSED(_o);
    Q_UNUSED(_id);
    Q_UNUSED(_c);
    Q_UNUSED(_a);
}

QT_INIT_METAOBJECT const QMetaObject QCPItemLine::staticMetaObject = { {
    &QCPAbstractItem::staticMetaObject,
    qt_meta_stringdata_QCPItemLine.data,
    qt_meta_data_QCPItemLine,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *QCPItemLine::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *QCPItemLine::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_QCPItemLine.stringdata0))
        return static_cast<void*>(this);
    return QCPAbstractItem::qt_metacast(_clname);
}

int QCPItemLine::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QCPAbstractItem::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    
#ifndef QT_NO_PROPERTIES
    if (_c == QMetaObject::ReadProperty || _c == QMetaObject::WriteProperty
            || _c == QMetaObject::ResetProperty || _c == QMetaObject::RegisterPropertyMetaType) {
        qt_static_metacall(this, _c, _id, _a);
        _id -= 4;
    } else if (_c == QMetaObject::QueryPropertyDesignable) {
        _id -= 4;
    } else if (_c == QMetaObject::QueryPropertyScriptable) {
        _id -= 4;
    } else if (_c == QMetaObject::QueryPropertyStored) {
        _id -= 4;
    } else if (_c == QMetaObject::QueryPropertyEditable) {
        _id -= 4;
    } else if (_c == QMetaObject::QueryPropertyUser) {
        _id -= 4;
    }
#endif // QT_NO_PROPERTIES
    return _id;
}
struct qt_meta_stringdata_QCPItemCurve_t {
    QByteArrayData data[6];
    char stringdata0[53];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_QCPItemCurve_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_QCPItemCurve_t qt_meta_stringdata_QCPItemCurve = {
    {
QT_MOC_LITERAL(0, 0, 12), // "QCPItemCurve"
QT_MOC_LITERAL(1, 13, 3), // "pen"
QT_MOC_LITERAL(2, 17, 11), // "selectedPen"
QT_MOC_LITERAL(3, 29, 4), // "head"
QT_MOC_LITERAL(4, 34, 13), // "QCPLineEnding"
QT_MOC_LITERAL(5, 48, 4) // "tail"

    },
    "QCPItemCurve\0pen\0selectedPen\0head\0"
    "QCPLineEnding\0tail"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_QCPItemCurve[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
       0,    0, // methods
       4,   14, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

 // properties: name, type, flags
       1, QMetaType::QPen, 0x00095103,
       2, QMetaType::QPen, 0x00095103,
       3, 0x80000000 | 4, 0x0009510b,
       5, 0x80000000 | 4, 0x0009510b,

       0        // eod
};

void QCPItemCurve::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{

#ifndef QT_NO_PROPERTIES
    if (_c == QMetaObject::ReadProperty) {
        auto *_t = static_cast<QCPItemCurve *>(_o);
        Q_UNUSED(_t)
        void *_v = _a[0];
        switch (_id) {
        case 0: *reinterpret_cast< QPen*>(_v) = _t->pen(); break;
        case 1: *reinterpret_cast< QPen*>(_v) = _t->selectedPen(); break;
        case 2: *reinterpret_cast< QCPLineEnding*>(_v) = _t->head(); break;
        case 3: *reinterpret_cast< QCPLineEnding*>(_v) = _t->tail(); break;
        default: break;
        }
    } else if (_c == QMetaObject::WriteProperty) {
        auto *_t = static_cast<QCPItemCurve *>(_o);
        Q_UNUSED(_t)
        void *_v = _a[0];
        switch (_id) {
        case 0: _t->setPen(*reinterpret_cast< QPen*>(_v)); break;
        case 1: _t->setSelectedPen(*reinterpret_cast< QPen*>(_v)); break;
        case 2: _t->setHead(*reinterpret_cast< QCPLineEnding*>(_v)); break;
        case 3: _t->setTail(*reinterpret_cast< QCPLineEnding*>(_v)); break;
        default: break;
        }
    } else if (_c == QMetaObject::ResetProperty) {
    }
#endif // QT_NO_PROPERTIES
    Q_UNUSED(_o);
    Q_UNUSED(_id);
    Q_UNUSED(_c);
    Q_UNUSED(_a);
}

QT_INIT_METAOBJECT const QMetaObject QCPItemCurve::staticMetaObject = { {
    &QCPAbstractItem::staticMetaObject,
    qt_meta_stringdata_QCPItemCurve.data,
    qt_meta_data_QCPItemCurve,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *QCPItemCurve::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *QCPItemCurve::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_QCPItemCurve.stringdata0))
        return static_cast<void*>(this);
    return QCPAbstractItem::qt_metacast(_clname);
}

int QCPItemCurve::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QCPAbstractItem::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    
#ifndef QT_NO_PROPERTIES
    if (_c == QMetaObject::ReadProperty || _c == QMetaObject::WriteProperty
            || _c == QMetaObject::ResetProperty || _c == QMetaObject::RegisterPropertyMetaType) {
        qt_static_metacall(this, _c, _id, _a);
        _id -= 4;
    } else if (_c == QMetaObject::QueryPropertyDesignable) {
        _id -= 4;
    } else if (_c == QMetaObject::QueryPropertyScriptable) {
        _id -= 4;
    } else if (_c == QMetaObject::QueryPropertyStored) {
        _id -= 4;
    } else if (_c == QMetaObject::QueryPropertyEditable) {
        _id -= 4;
    } else if (_c == QMetaObject::QueryPropertyUser) {
        _id -= 4;
    }
#endif // QT_NO_PROPERTIES
    return _id;
}
struct qt_meta_stringdata_QCPItemRect_t {
    QByteArrayData data[5];
    char stringdata0[48];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_QCPItemRect_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_QCPItemRect_t qt_meta_stringdata_QCPItemRect = {
    {
QT_MOC_LITERAL(0, 0, 11), // "QCPItemRect"
QT_MOC_LITERAL(1, 12, 3), // "pen"
QT_MOC_LITERAL(2, 16, 11), // "selectedPen"
QT_MOC_LITERAL(3, 28, 5), // "brush"
QT_MOC_LITERAL(4, 34, 13) // "selectedBrush"

    },
    "QCPItemRect\0pen\0selectedPen\0brush\0"
    "selectedBrush"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_QCPItemRect[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
       0,    0, // methods
       4,   14, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

 // properties: name, type, flags
       1, QMetaType::QPen, 0x00095103,
       2, QMetaType::QPen, 0x00095103,
       3, QMetaType::QBrush, 0x00095103,
       4, QMetaType::QBrush, 0x00095103,

       0        // eod
};

void QCPItemRect::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{

#ifndef QT_NO_PROPERTIES
    if (_c == QMetaObject::ReadProperty) {
        auto *_t = static_cast<QCPItemRect *>(_o);
        Q_UNUSED(_t)
        void *_v = _a[0];
        switch (_id) {
        case 0: *reinterpret_cast< QPen*>(_v) = _t->pen(); break;
        case 1: *reinterpret_cast< QPen*>(_v) = _t->selectedPen(); break;
        case 2: *reinterpret_cast< QBrush*>(_v) = _t->brush(); break;
        case 3: *reinterpret_cast< QBrush*>(_v) = _t->selectedBrush(); break;
        default: break;
        }
    } else if (_c == QMetaObject::WriteProperty) {
        auto *_t = static_cast<QCPItemRect *>(_o);
        Q_UNUSED(_t)
        void *_v = _a[0];
        switch (_id) {
        case 0: _t->setPen(*reinterpret_cast< QPen*>(_v)); break;
        case 1: _t->setSelectedPen(*reinterpret_cast< QPen*>(_v)); break;
        case 2: _t->setBrush(*reinterpret_cast< QBrush*>(_v)); break;
        case 3: _t->setSelectedBrush(*reinterpret_cast< QBrush*>(_v)); break;
        default: break;
        }
    } else if (_c == QMetaObject::ResetProperty) {
    }
#endif // QT_NO_PROPERTIES
    Q_UNUSED(_o);
    Q_UNUSED(_id);
    Q_UNUSED(_c);
    Q_UNUSED(_a);
}

QT_INIT_METAOBJECT const QMetaObject QCPItemRect::staticMetaObject = { {
    &QCPAbstractItem::staticMetaObject,
    qt_meta_stringdata_QCPItemRect.data,
    qt_meta_data_QCPItemRect,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *QCPItemRect::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *QCPItemRect::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_QCPItemRect.stringdata0))
        return static_cast<void*>(this);
    return QCPAbstractItem::qt_metacast(_clname);
}

int QCPItemRect::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QCPAbstractItem::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    
#ifndef QT_NO_PROPERTIES
    if (_c == QMetaObject::ReadProperty || _c == QMetaObject::WriteProperty
            || _c == QMetaObject::ResetProperty || _c == QMetaObject::RegisterPropertyMetaType) {
        qt_static_metacall(this, _c, _id, _a);
        _id -= 4;
    } else if (_c == QMetaObject::QueryPropertyDesignable) {
        _id -= 4;
    } else if (_c == QMetaObject::QueryPropertyScriptable) {
        _id -= 4;
    } else if (_c == QMetaObject::QueryPropertyStored) {
        _id -= 4;
    } else if (_c == QMetaObject::QueryPropertyEditable) {
        _id -= 4;
    } else if (_c == QMetaObject::QueryPropertyUser) {
        _id -= 4;
    }
#endif // QT_NO_PROPERTIES
    return _id;
}
struct qt_meta_stringdata_QCPItemText_t {
    QByteArrayData data[16];
    char stringdata0[163];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_QCPItemText_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_QCPItemText_t qt_meta_stringdata_QCPItemText = {
    {
QT_MOC_LITERAL(0, 0, 11), // "QCPItemText"
QT_MOC_LITERAL(1, 12, 5), // "color"
QT_MOC_LITERAL(2, 18, 13), // "selectedColor"
QT_MOC_LITERAL(3, 32, 3), // "pen"
QT_MOC_LITERAL(4, 36, 11), // "selectedPen"
QT_MOC_LITERAL(5, 48, 5), // "brush"
QT_MOC_LITERAL(6, 54, 13), // "selectedBrush"
QT_MOC_LITERAL(7, 68, 4), // "font"
QT_MOC_LITERAL(8, 73, 12), // "selectedFont"
QT_MOC_LITERAL(9, 86, 4), // "text"
QT_MOC_LITERAL(10, 91, 17), // "positionAlignment"
QT_MOC_LITERAL(11, 109, 13), // "Qt::Alignment"
QT_MOC_LITERAL(12, 123, 13), // "textAlignment"
QT_MOC_LITERAL(13, 137, 8), // "rotation"
QT_MOC_LITERAL(14, 146, 7), // "padding"
QT_MOC_LITERAL(15, 154, 8) // "QMargins"

    },
    "QCPItemText\0color\0selectedColor\0pen\0"
    "selectedPen\0brush\0selectedBrush\0font\0"
    "selectedFont\0text\0positionAlignment\0"
    "Qt::Alignment\0textAlignment\0rotation\0"
    "padding\0QMargins"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_QCPItemText[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
       0,    0, // methods
      13,   14, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

 // properties: name, type, flags
       1, QMetaType::QColor, 0x00095103,
       2, QMetaType::QColor, 0x00095103,
       3, QMetaType::QPen, 0x00095103,
       4, QMetaType::QPen, 0x00095103,
       5, QMetaType::QBrush, 0x00095103,
       6, QMetaType::QBrush, 0x00095103,
       7, QMetaType::QFont, 0x00095103,
       8, QMetaType::QFont, 0x00095103,
       9, QMetaType::QString, 0x00095103,
      10, 0x80000000 | 11, 0x0009510b,
      12, 0x80000000 | 11, 0x0009510b,
      13, QMetaType::Double, 0x00095103,
      14, 0x80000000 | 15, 0x0009510b,

       0        // eod
};

void QCPItemText::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{

#ifndef QT_NO_PROPERTIES
    if (_c == QMetaObject::ReadProperty) {
        auto *_t = static_cast<QCPItemText *>(_o);
        Q_UNUSED(_t)
        void *_v = _a[0];
        switch (_id) {
        case 0: *reinterpret_cast< QColor*>(_v) = _t->color(); break;
        case 1: *reinterpret_cast< QColor*>(_v) = _t->selectedColor(); break;
        case 2: *reinterpret_cast< QPen*>(_v) = _t->pen(); break;
        case 3: *reinterpret_cast< QPen*>(_v) = _t->selectedPen(); break;
        case 4: *reinterpret_cast< QBrush*>(_v) = _t->brush(); break;
        case 5: *reinterpret_cast< QBrush*>(_v) = _t->selectedBrush(); break;
        case 6: *reinterpret_cast< QFont*>(_v) = _t->font(); break;
        case 7: *reinterpret_cast< QFont*>(_v) = _t->selectedFont(); break;
        case 8: *reinterpret_cast< QString*>(_v) = _t->text(); break;
        case 9: *reinterpret_cast< Qt::Alignment*>(_v) = _t->positionAlignment(); break;
        case 10: *reinterpret_cast< Qt::Alignment*>(_v) = _t->textAlignment(); break;
        case 11: *reinterpret_cast< double*>(_v) = _t->rotation(); break;
        case 12: *reinterpret_cast< QMargins*>(_v) = _t->padding(); break;
        default: break;
        }
    } else if (_c == QMetaObject::WriteProperty) {
        auto *_t = static_cast<QCPItemText *>(_o);
        Q_UNUSED(_t)
        void *_v = _a[0];
        switch (_id) {
        case 0: _t->setColor(*reinterpret_cast< QColor*>(_v)); break;
        case 1: _t->setSelectedColor(*reinterpret_cast< QColor*>(_v)); break;
        case 2: _t->setPen(*reinterpret_cast< QPen*>(_v)); break;
        case 3: _t->setSelectedPen(*reinterpret_cast< QPen*>(_v)); break;
        case 4: _t->setBrush(*reinterpret_cast< QBrush*>(_v)); break;
        case 5: _t->setSelectedBrush(*reinterpret_cast< QBrush*>(_v)); break;
        case 6: _t->setFont(*reinterpret_cast< QFont*>(_v)); break;
        case 7: _t->setSelectedFont(*reinterpret_cast< QFont*>(_v)); break;
        case 8: _t->setText(*reinterpret_cast< QString*>(_v)); break;
        case 9: _t->setPositionAlignment(*reinterpret_cast< Qt::Alignment*>(_v)); break;
        case 10: _t->setTextAlignment(*reinterpret_cast< Qt::Alignment*>(_v)); break;
        case 11: _t->setRotation(*reinterpret_cast< double*>(_v)); break;
        case 12: _t->setPadding(*reinterpret_cast< QMargins*>(_v)); break;
        default: break;
        }
    } else if (_c == QMetaObject::ResetProperty) {
    }
#endif // QT_NO_PROPERTIES
    Q_UNUSED(_o);
    Q_UNUSED(_id);
    Q_UNUSED(_c);
    Q_UNUSED(_a);
}

QT_INIT_METAOBJECT const QMetaObject QCPItemText::staticMetaObject = { {
    &QCPAbstractItem::staticMetaObject,
    qt_meta_stringdata_QCPItemText.data,
    qt_meta_data_QCPItemText,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *QCPItemText::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *QCPItemText::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_QCPItemText.stringdata0))
        return static_cast<void*>(this);
    return QCPAbstractItem::qt_metacast(_clname);
}

int QCPItemText::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QCPAbstractItem::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    
#ifndef QT_NO_PROPERTIES
    if (_c == QMetaObject::ReadProperty || _c == QMetaObject::WriteProperty
            || _c == QMetaObject::ResetProperty || _c == QMetaObject::RegisterPropertyMetaType) {
        qt_static_metacall(this, _c, _id, _a);
        _id -= 13;
    } else if (_c == QMetaObject::QueryPropertyDesignable) {
        _id -= 13;
    } else if (_c == QMetaObject::QueryPropertyScriptable) {
        _id -= 13;
    } else if (_c == QMetaObject::QueryPropertyStored) {
        _id -= 13;
    } else if (_c == QMetaObject::QueryPropertyEditable) {
        _id -= 13;
    } else if (_c == QMetaObject::QueryPropertyUser) {
        _id -= 13;
    }
#endif // QT_NO_PROPERTIES
    return _id;
}
struct qt_meta_stringdata_QCPItemEllipse_t {
    QByteArrayData data[5];
    char stringdata0[51];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_QCPItemEllipse_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_QCPItemEllipse_t qt_meta_stringdata_QCPItemEllipse = {
    {
QT_MOC_LITERAL(0, 0, 14), // "QCPItemEllipse"
QT_MOC_LITERAL(1, 15, 3), // "pen"
QT_MOC_LITERAL(2, 19, 11), // "selectedPen"
QT_MOC_LITERAL(3, 31, 5), // "brush"
QT_MOC_LITERAL(4, 37, 13) // "selectedBrush"

    },
    "QCPItemEllipse\0pen\0selectedPen\0brush\0"
    "selectedBrush"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_QCPItemEllipse[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
       0,    0, // methods
       4,   14, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

 // properties: name, type, flags
       1, QMetaType::QPen, 0x00095103,
       2, QMetaType::QPen, 0x00095103,
       3, QMetaType::QBrush, 0x00095103,
       4, QMetaType::QBrush, 0x00095103,

       0        // eod
};

void QCPItemEllipse::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{

#ifndef QT_NO_PROPERTIES
    if (_c == QMetaObject::ReadProperty) {
        auto *_t = static_cast<QCPItemEllipse *>(_o);
        Q_UNUSED(_t)
        void *_v = _a[0];
        switch (_id) {
        case 0: *reinterpret_cast< QPen*>(_v) = _t->pen(); break;
        case 1: *reinterpret_cast< QPen*>(_v) = _t->selectedPen(); break;
        case 2: *reinterpret_cast< QBrush*>(_v) = _t->brush(); break;
        case 3: *reinterpret_cast< QBrush*>(_v) = _t->selectedBrush(); break;
        default: break;
        }
    } else if (_c == QMetaObject::WriteProperty) {
        auto *_t = static_cast<QCPItemEllipse *>(_o);
        Q_UNUSED(_t)
        void *_v = _a[0];
        switch (_id) {
        case 0: _t->setPen(*reinterpret_cast< QPen*>(_v)); break;
        case 1: _t->setSelectedPen(*reinterpret_cast< QPen*>(_v)); break;
        case 2: _t->setBrush(*reinterpret_cast< QBrush*>(_v)); break;
        case 3: _t->setSelectedBrush(*reinterpret_cast< QBrush*>(_v)); break;
        default: break;
        }
    } else if (_c == QMetaObject::ResetProperty) {
    }
#endif // QT_NO_PROPERTIES
    Q_UNUSED(_o);
    Q_UNUSED(_id);
    Q_UNUSED(_c);
    Q_UNUSED(_a);
}

QT_INIT_METAOBJECT const QMetaObject QCPItemEllipse::staticMetaObject = { {
    &QCPAbstractItem::staticMetaObject,
    qt_meta_stringdata_QCPItemEllipse.data,
    qt_meta_data_QCPItemEllipse,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *QCPItemEllipse::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *QCPItemEllipse::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_QCPItemEllipse.stringdata0))
        return static_cast<void*>(this);
    return QCPAbstractItem::qt_metacast(_clname);
}

int QCPItemEllipse::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QCPAbstractItem::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    
#ifndef QT_NO_PROPERTIES
    if (_c == QMetaObject::ReadProperty || _c == QMetaObject::WriteProperty
            || _c == QMetaObject::ResetProperty || _c == QMetaObject::RegisterPropertyMetaType) {
        qt_static_metacall(this, _c, _id, _a);
        _id -= 4;
    } else if (_c == QMetaObject::QueryPropertyDesignable) {
        _id -= 4;
    } else if (_c == QMetaObject::QueryPropertyScriptable) {
        _id -= 4;
    } else if (_c == QMetaObject::QueryPropertyStored) {
        _id -= 4;
    } else if (_c == QMetaObject::QueryPropertyEditable) {
        _id -= 4;
    } else if (_c == QMetaObject::QueryPropertyUser) {
        _id -= 4;
    }
#endif // QT_NO_PROPERTIES
    return _id;
}
struct qt_meta_stringdata_QCPItemPixmap_t {
    QByteArrayData data[9];
    char stringdata0[122];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_QCPItemPixmap_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_QCPItemPixmap_t qt_meta_stringdata_QCPItemPixmap = {
    {
QT_MOC_LITERAL(0, 0, 13), // "QCPItemPixmap"
QT_MOC_LITERAL(1, 14, 6), // "pixmap"
QT_MOC_LITERAL(2, 21, 6), // "scaled"
QT_MOC_LITERAL(3, 28, 15), // "aspectRatioMode"
QT_MOC_LITERAL(4, 44, 19), // "Qt::AspectRatioMode"
QT_MOC_LITERAL(5, 64, 18), // "transformationMode"
QT_MOC_LITERAL(6, 83, 22), // "Qt::TransformationMode"
QT_MOC_LITERAL(7, 106, 3), // "pen"
QT_MOC_LITERAL(8, 110, 11) // "selectedPen"

    },
    "QCPItemPixmap\0pixmap\0scaled\0aspectRatioMode\0"
    "Qt::AspectRatioMode\0transformationMode\0"
    "Qt::TransformationMode\0pen\0selectedPen"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_QCPItemPixmap[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
       0,    0, // methods
       6,   14, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

 // properties: name, type, flags
       1, QMetaType::QPixmap, 0x00095103,
       2, QMetaType::Bool, 0x00095103,
       3, 0x80000000 | 4, 0x00095009,
       5, 0x80000000 | 6, 0x00095009,
       7, QMetaType::QPen, 0x00095103,
       8, QMetaType::QPen, 0x00095103,

       0        // eod
};

void QCPItemPixmap::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{

#ifndef QT_NO_PROPERTIES
    if (_c == QMetaObject::ReadProperty) {
        auto *_t = static_cast<QCPItemPixmap *>(_o);
        Q_UNUSED(_t)
        void *_v = _a[0];
        switch (_id) {
        case 0: *reinterpret_cast< QPixmap*>(_v) = _t->pixmap(); break;
        case 1: *reinterpret_cast< bool*>(_v) = _t->scaled(); break;
        case 2: *reinterpret_cast< Qt::AspectRatioMode*>(_v) = _t->aspectRatioMode(); break;
        case 3: *reinterpret_cast< Qt::TransformationMode*>(_v) = _t->transformationMode(); break;
        case 4: *reinterpret_cast< QPen*>(_v) = _t->pen(); break;
        case 5: *reinterpret_cast< QPen*>(_v) = _t->selectedPen(); break;
        default: break;
        }
    } else if (_c == QMetaObject::WriteProperty) {
        auto *_t = static_cast<QCPItemPixmap *>(_o);
        Q_UNUSED(_t)
        void *_v = _a[0];
        switch (_id) {
        case 0: _t->setPixmap(*reinterpret_cast< QPixmap*>(_v)); break;
        case 1: _t->setScaled(*reinterpret_cast< bool*>(_v)); break;
        case 4: _t->setPen(*reinterpret_cast< QPen*>(_v)); break;
        case 5: _t->setSelectedPen(*reinterpret_cast< QPen*>(_v)); break;
        default: break;
        }
    } else if (_c == QMetaObject::ResetProperty) {
    }
#endif // QT_NO_PROPERTIES
    Q_UNUSED(_o);
    Q_UNUSED(_id);
    Q_UNUSED(_c);
    Q_UNUSED(_a);
}

QT_INIT_METAOBJECT const QMetaObject QCPItemPixmap::staticMetaObject = { {
    &QCPAbstractItem::staticMetaObject,
    qt_meta_stringdata_QCPItemPixmap.data,
    qt_meta_data_QCPItemPixmap,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *QCPItemPixmap::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *QCPItemPixmap::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_QCPItemPixmap.stringdata0))
        return static_cast<void*>(this);
    return QCPAbstractItem::qt_metacast(_clname);
}

int QCPItemPixmap::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QCPAbstractItem::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    
#ifndef QT_NO_PROPERTIES
    if (_c == QMetaObject::ReadProperty || _c == QMetaObject::WriteProperty
            || _c == QMetaObject::ResetProperty || _c == QMetaObject::RegisterPropertyMetaType) {
        qt_static_metacall(this, _c, _id, _a);
        _id -= 6;
    } else if (_c == QMetaObject::QueryPropertyDesignable) {
        _id -= 6;
    } else if (_c == QMetaObject::QueryPropertyScriptable) {
        _id -= 6;
    } else if (_c == QMetaObject::QueryPropertyStored) {
        _id -= 6;
    } else if (_c == QMetaObject::QueryPropertyEditable) {
        _id -= 6;
    } else if (_c == QMetaObject::QueryPropertyUser) {
        _id -= 6;
    }
#endif // QT_NO_PROPERTIES
    return _id;
}
struct qt_meta_stringdata_QCPItemTracer_t {
    QByteArrayData data[17];
    char stringdata0[156];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_QCPItemTracer_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_QCPItemTracer_t qt_meta_stringdata_QCPItemTracer = {
    {
QT_MOC_LITERAL(0, 0, 13), // "QCPItemTracer"
QT_MOC_LITERAL(1, 14, 3), // "pen"
QT_MOC_LITERAL(2, 18, 11), // "selectedPen"
QT_MOC_LITERAL(3, 30, 5), // "brush"
QT_MOC_LITERAL(4, 36, 13), // "selectedBrush"
QT_MOC_LITERAL(5, 50, 4), // "size"
QT_MOC_LITERAL(6, 55, 5), // "style"
QT_MOC_LITERAL(7, 61, 11), // "TracerStyle"
QT_MOC_LITERAL(8, 73, 5), // "graph"
QT_MOC_LITERAL(9, 79, 9), // "QCPGraph*"
QT_MOC_LITERAL(10, 89, 8), // "graphKey"
QT_MOC_LITERAL(11, 98, 13), // "interpolating"
QT_MOC_LITERAL(12, 112, 6), // "tsNone"
QT_MOC_LITERAL(13, 119, 6), // "tsPlus"
QT_MOC_LITERAL(14, 126, 11), // "tsCrosshair"
QT_MOC_LITERAL(15, 138, 8), // "tsCircle"
QT_MOC_LITERAL(16, 147, 8) // "tsSquare"

    },
    "QCPItemTracer\0pen\0selectedPen\0brush\0"
    "selectedBrush\0size\0style\0TracerStyle\0"
    "graph\0QCPGraph*\0graphKey\0interpolating\0"
    "tsNone\0tsPlus\0tsCrosshair\0tsCircle\0"
    "tsSquare"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_QCPItemTracer[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
       0,    0, // methods
       9,   14, // properties
       1,   41, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

 // properties: name, type, flags
       1, QMetaType::QPen, 0x00095103,
       2, QMetaType::QPen, 0x00095103,
       3, QMetaType::QBrush, 0x00095103,
       4, QMetaType::QBrush, 0x00095103,
       5, QMetaType::Double, 0x00095103,
       6, 0x80000000 | 7, 0x0009510b,
       8, 0x80000000 | 9, 0x0009510b,
      10, QMetaType::Double, 0x00095103,
      11, QMetaType::Bool, 0x00095103,

 // enums: name, alias, flags, count, data
       7,    7, 0x0,    5,   46,

 // enum data: key, value
      12, uint(QCPItemTracer::tsNone),
      13, uint(QCPItemTracer::tsPlus),
      14, uint(QCPItemTracer::tsCrosshair),
      15, uint(QCPItemTracer::tsCircle),
      16, uint(QCPItemTracer::tsSquare),

       0        // eod
};

void QCPItemTracer::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::RegisterPropertyMetaType) {
        switch (_id) {
        default: *reinterpret_cast<int*>(_a[0]) = -1; break;
        case 6:
            *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< QCPGraph* >(); break;
        }
    }

#ifndef QT_NO_PROPERTIES
    else if (_c == QMetaObject::ReadProperty) {
        auto *_t = static_cast<QCPItemTracer *>(_o);
        Q_UNUSED(_t)
        void *_v = _a[0];
        switch (_id) {
        case 0: *reinterpret_cast< QPen*>(_v) = _t->pen(); break;
        case 1: *reinterpret_cast< QPen*>(_v) = _t->selectedPen(); break;
        case 2: *reinterpret_cast< QBrush*>(_v) = _t->brush(); break;
        case 3: *reinterpret_cast< QBrush*>(_v) = _t->selectedBrush(); break;
        case 4: *reinterpret_cast< double*>(_v) = _t->size(); break;
        case 5: *reinterpret_cast< TracerStyle*>(_v) = _t->style(); break;
        case 6: *reinterpret_cast< QCPGraph**>(_v) = _t->graph(); break;
        case 7: *reinterpret_cast< double*>(_v) = _t->graphKey(); break;
        case 8: *reinterpret_cast< bool*>(_v) = _t->interpolating(); break;
        default: break;
        }
    } else if (_c == QMetaObject::WriteProperty) {
        auto *_t = static_cast<QCPItemTracer *>(_o);
        Q_UNUSED(_t)
        void *_v = _a[0];
        switch (_id) {
        case 0: _t->setPen(*reinterpret_cast< QPen*>(_v)); break;
        case 1: _t->setSelectedPen(*reinterpret_cast< QPen*>(_v)); break;
        case 2: _t->setBrush(*reinterpret_cast< QBrush*>(_v)); break;
        case 3: _t->setSelectedBrush(*reinterpret_cast< QBrush*>(_v)); break;
        case 4: _t->setSize(*reinterpret_cast< double*>(_v)); break;
        case 5: _t->setStyle(*reinterpret_cast< TracerStyle*>(_v)); break;
        case 6: _t->setGraph(*reinterpret_cast< QCPGraph**>(_v)); break;
        case 7: _t->setGraphKey(*reinterpret_cast< double*>(_v)); break;
        case 8: _t->setInterpolating(*reinterpret_cast< bool*>(_v)); break;
        default: break;
        }
    } else if (_c == QMetaObject::ResetProperty) {
    }
#endif // QT_NO_PROPERTIES
    Q_UNUSED(_o);
}

QT_INIT_METAOBJECT const QMetaObject QCPItemTracer::staticMetaObject = { {
    &QCPAbstractItem::staticMetaObject,
    qt_meta_stringdata_QCPItemTracer.data,
    qt_meta_data_QCPItemTracer,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *QCPItemTracer::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *QCPItemTracer::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_QCPItemTracer.stringdata0))
        return static_cast<void*>(this);
    return QCPAbstractItem::qt_metacast(_clname);
}

int QCPItemTracer::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QCPAbstractItem::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    
#ifndef QT_NO_PROPERTIES
    if (_c == QMetaObject::ReadProperty || _c == QMetaObject::WriteProperty
            || _c == QMetaObject::ResetProperty || _c == QMetaObject::RegisterPropertyMetaType) {
        qt_static_metacall(this, _c, _id, _a);
        _id -= 9;
    } else if (_c == QMetaObject::QueryPropertyDesignable) {
        _id -= 9;
    } else if (_c == QMetaObject::QueryPropertyScriptable) {
        _id -= 9;
    } else if (_c == QMetaObject::QueryPropertyStored) {
        _id -= 9;
    } else if (_c == QMetaObject::QueryPropertyEditable) {
        _id -= 9;
    } else if (_c == QMetaObject::QueryPropertyUser) {
        _id -= 9;
    }
#endif // QT_NO_PROPERTIES
    return _id;
}
struct qt_meta_stringdata_QCPItemBracket_t {
    QByteArrayData data[10];
    char stringdata0[97];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_QCPItemBracket_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_QCPItemBracket_t qt_meta_stringdata_QCPItemBracket = {
    {
QT_MOC_LITERAL(0, 0, 14), // "QCPItemBracket"
QT_MOC_LITERAL(1, 15, 3), // "pen"
QT_MOC_LITERAL(2, 19, 11), // "selectedPen"
QT_MOC_LITERAL(3, 31, 6), // "length"
QT_MOC_LITERAL(4, 38, 5), // "style"
QT_MOC_LITERAL(5, 44, 12), // "BracketStyle"
QT_MOC_LITERAL(6, 57, 8), // "bsSquare"
QT_MOC_LITERAL(7, 66, 7), // "bsRound"
QT_MOC_LITERAL(8, 74, 7), // "bsCurly"
QT_MOC_LITERAL(9, 82, 14) // "bsCalligraphic"

    },
    "QCPItemBracket\0pen\0selectedPen\0length\0"
    "style\0BracketStyle\0bsSquare\0bsRound\0"
    "bsCurly\0bsCalligraphic"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_QCPItemBracket[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
       0,    0, // methods
       4,   14, // properties
       1,   26, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

 // properties: name, type, flags
       1, QMetaType::QPen, 0x00095103,
       2, QMetaType::QPen, 0x00095103,
       3, QMetaType::Double, 0x00095103,
       4, 0x80000000 | 5, 0x0009510b,

 // enums: name, alias, flags, count, data
       5,    5, 0x0,    4,   31,

 // enum data: key, value
       6, uint(QCPItemBracket::bsSquare),
       7, uint(QCPItemBracket::bsRound),
       8, uint(QCPItemBracket::bsCurly),
       9, uint(QCPItemBracket::bsCalligraphic),

       0        // eod
};

void QCPItemBracket::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{

#ifndef QT_NO_PROPERTIES
    if (_c == QMetaObject::ReadProperty) {
        auto *_t = static_cast<QCPItemBracket *>(_o);
        Q_UNUSED(_t)
        void *_v = _a[0];
        switch (_id) {
        case 0: *reinterpret_cast< QPen*>(_v) = _t->pen(); break;
        case 1: *reinterpret_cast< QPen*>(_v) = _t->selectedPen(); break;
        case 2: *reinterpret_cast< double*>(_v) = _t->length(); break;
        case 3: *reinterpret_cast< BracketStyle*>(_v) = _t->style(); break;
        default: break;
        }
    } else if (_c == QMetaObject::WriteProperty) {
        auto *_t = static_cast<QCPItemBracket *>(_o);
        Q_UNUSED(_t)
        void *_v = _a[0];
        switch (_id) {
        case 0: _t->setPen(*reinterpret_cast< QPen*>(_v)); break;
        case 1: _t->setSelectedPen(*reinterpret_cast< QPen*>(_v)); break;
        case 2: _t->setLength(*reinterpret_cast< double*>(_v)); break;
        case 3: _t->setStyle(*reinterpret_cast< BracketStyle*>(_v)); break;
        default: break;
        }
    } else if (_c == QMetaObject::ResetProperty) {
    }
#endif // QT_NO_PROPERTIES
    Q_UNUSED(_o);
    Q_UNUSED(_id);
    Q_UNUSED(_c);
    Q_UNUSED(_a);
}

QT_INIT_METAOBJECT const QMetaObject QCPItemBracket::staticMetaObject = { {
    &QCPAbstractItem::staticMetaObject,
    qt_meta_stringdata_QCPItemBracket.data,
    qt_meta_data_QCPItemBracket,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *QCPItemBracket::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *QCPItemBracket::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_QCPItemBracket.stringdata0))
        return static_cast<void*>(this);
    return QCPAbstractItem::qt_metacast(_clname);
}

int QCPItemBracket::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QCPAbstractItem::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    
#ifndef QT_NO_PROPERTIES
    if (_c == QMetaObject::ReadProperty || _c == QMetaObject::WriteProperty
            || _c == QMetaObject::ResetProperty || _c == QMetaObject::RegisterPropertyMetaType) {
        qt_static_metacall(this, _c, _id, _a);
        _id -= 4;
    } else if (_c == QMetaObject::QueryPropertyDesignable) {
        _id -= 4;
    } else if (_c == QMetaObject::QueryPropertyScriptable) {
        _id -= 4;
    } else if (_c == QMetaObject::QueryPropertyStored) {
        _id -= 4;
    } else if (_c == QMetaObject::QueryPropertyEditable) {
        _id -= 4;
    } else if (_c == QMetaObject::QueryPropertyUser) {
        _id -= 4;
    }
#endif // QT_NO_PROPERTIES
    return _id;
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
